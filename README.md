# 📺 RTSP Compression Dashboard

A web-based interface for capturing, compressing, and analyzing RTSP video streams with automatic S3 upload and bandwidth analysis.

## 🚀 Features

- **Live RTSP Stream Processing**: Capture both raw and compressed versions simultaneously
- **Real-time Metrics**: Monitor file sizes and compression ratios during capture
- **S3 Integration**: Automatic upload to separate buckets for original and compressed streams
- **Bandwidth Analysis**: Calculate compression ratios and bandwidth savings
- **Web Dashboard**: Modern, responsive interface with real-time updates
- **Process Management**: Start, stop, and monitor compression processes
- **Security**: Input sanitization and process isolation

## 📋 Prerequisites

- **Node.js** (v14 or higher)
- **FFmpeg** (for video processing)
- **AWS CLI** configured with appropriate credentials
- **Bash** shell environment

## 🛠️ Installation

1. **Clone or navigate to the project directory**
   ```bash
   cd /path/to/zmt-live
   ```

2. **Install Node.js dependencies**
   ```bash
   npm install
   ```

3. **Configure AWS CLI** (if not already done)
   ```bash
   aws configure
   ```

4. **Make shell scripts executable**
   ```bash
   chmod +x *.sh
   ```

## 🎯 Usage

### Starting the Dashboard

1. **Start the server**
   ```bash
   npm start
   ```
   
   Or for development with auto-reload:
   ```bash
   npm run dev
   ```

2. **Open your browser**
   Navigate to: `http://localhost:3000`

### Using the Dashboard

1. **Enter RTSP URL**: Input your camera's RTSP stream URL
   - Example: `rtsp://your.camera.ip:554/stream1`

2. **Set Duration**: Choose capture duration (1-15 minutes)

3. **Start Compression**: Click "Start Compression" to begin

4. **Monitor Progress**: Watch real-time metrics and logs

5. **View Results**: Check the final report with compression statistics

### Manual Script Usage

You can also run the compression scripts directly:

```bash
# Run both streams with shared timestamp
./run_both_streams.sh rtsp://your.camera.ip:554/stream1 00:03:00

# Or run individual scripts
./compress_code_video_stream.sh rtsp://url 00:03:00 [timestamp]
./save_rtsp_raw_stream.sh rtsp://url 00:03:00 [timestamp]
```

## 📁 Project Structure

```
zmt-live/
├── server.js                 # Express server and API
├── package.json              # Node.js dependencies
├── run_both_streams.sh       # Main script (dual capture + S3 upload)
├── compress_code_video_stream.sh  # Compression script
├── save_rtsp_raw_stream.sh   # Raw capture script
├── public/                   # Web dashboard files
│   ├── index.html           # Main dashboard page
│   ├── styles.css           # Dashboard styling
│   └── app.js               # Frontend JavaScript
├── original/stream/         # Raw stream files (temporary)
├── encoded/stream/          # Compressed files (temporary)
└── logs/                    # Process logs
```

## ☁️ S3 Configuration

The system automatically creates and uses these S3 buckets:

- **`original-streams`**: Raw RTSP captures
- **`compressed-streams`**: Compressed video files

Files are organized by date: `YYYY/MM/DD/filename.ts`

## 🔧 Configuration

### Compression Settings

Edit `compress_code_video_stream.sh` to modify compression parameters:

- **Video bitrate**: `-b:v 197k` (current: 197 kbps)
- **Audio bitrate**: `-b:a 64k` (current: 64 kbps)
- **Preset**: `-preset veryslow` (quality vs speed)

### Server Settings

Edit `server.js` to modify:

- **Port**: `const PORT = process.env.PORT || 3000`
- **WebSocket port**: `const wss = new WebSocket.Server({ port: 8080 })`
- **Max duration**: Currently limited to 15 minutes for safety

## 🔒 Security Features

- **Input sanitization**: RTSP URLs are validated with regex
- **Command injection prevention**: Parameters are properly escaped
- **Duration limits**: Maximum 15-minute captures
- **Process isolation**: Each session runs in isolated processes

## 📊 API Endpoints

- **POST** `/api/start-compression` - Start compression process
- **GET** `/api/status` - Get current system status
- **POST** `/api/stop-compression/:sessionId` - Stop specific process

## 🐛 Troubleshooting

### Common Issues

1. **"AWS CLI region not configured"**
   ```bash
   aws configure set region us-east-1
   ```

2. **"Permission denied" on scripts**
   ```bash
   chmod +x *.sh
   ```

3. **FFmpeg not found**
   ```bash
   # Ubuntu/Debian
   sudo apt install ffmpeg
   
   # macOS
   brew install ffmpeg
   ```

4. **Port already in use**
   - Change the port in `server.js`
   - Or kill existing processes: `pkill -f "node server.js"`

### Logs

- **Server logs**: Check the terminal running `node server.js`
- **Process logs**: Check the `logs/` directory
- **Browser logs**: Open browser developer tools (F12)

## 📈 Performance Tips

- Use wired network connection for stable RTSP streams
- Ensure sufficient disk space for temporary files
- Monitor AWS costs for S3 storage and transfer
- Consider using S3 lifecycle policies for automatic cleanup

## 🤝 Contributing

1. Test changes with sample RTSP streams
2. Ensure all shell scripts remain executable
3. Update documentation for any configuration changes
4. Test both web interface and direct script usage

## 📄 License

MIT License - see project files for details.
