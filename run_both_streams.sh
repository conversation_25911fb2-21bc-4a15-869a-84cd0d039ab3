#!/bin/bash
# ─────────────────────────────────────────────
# Script: run_both_streams.sh
# Purpose:
#   - Capture both raw and compressed RTSP streams
#   - Store locally using a shared timestamp
#   - Convert completed .ts files to MP4 format
#   - Store MP4 files locally for web serving
# ─────────────────────────────────────────────

# 📥 Input Arguments
RTSP_URL="$1"
CAPTURE_DURATION="$2"

if [ -z "$RTSP_URL" ] || [ -z "$CAPTURE_DURATION" ]; then
    echo "❗ Usage: $0 <RTSP_URL> <DURATION (HH:MM:SS)>"
    exit 1
fi

# 🕒 Shared timestamp and directory setup
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
DATE_PATH=$(date +"%Y/%m/%d")
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
RAW_DIR="$SCRIPT_DIR/original/stream"
ENCODED_DIR="$SCRIPT_DIR/encoded/stream"

# 📁 Local MP4 storage - no AWS/S3 configuration needed

# 🛑 Handle script interruption
cleanup_on_interrupt() {
    log "🛑 Interrupted. Cleaning up..."
    kill $COMPRESS_PID $RAW_PID 2>/dev/null
    exit 1
}
trap cleanup_on_interrupt INT TERM

# 🕓 Timestamped logger
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# ☁️ Create bucket if it doesn’t exist
# Function removed - no longer using S3 buckets

# 🎬 Convert .ts files to MP4 and store locally
convert_to_mp4_folder() {
    LOCAL_DIR="$1"
    STREAM_TYPE="$2"  # "original" or "compressed"

    if [ -d "$LOCAL_DIR" ]; then
        for FILE in "$LOCAL_DIR"/*.ts; do
            [ -e "$FILE" ] || continue  # skip if no files
            log "🔄 Converting $FILE to MP4 ($STREAM_TYPE)"

            # Use the local MP4 conversion script
            if ./convert_to_mp4_local.sh "$FILE" "$TIMESTAMP"; then
                log "✅ Converted to MP4. Deleting $FILE"
                rm -f "$FILE"
            else
                log "❌ MP4 conversion failed for $FILE"
            fi
        done
    else
        log "❌ Directory not found: $LOCAL_DIR"
    fi
}

# 📁 Ensure MP4 directory exists
mkdir -p mp4

# 🚀 Start both capture scripts with shared timestamp
log "▶️ Starting capture with timestamp $TIMESTAMP"

"$SCRIPT_DIR/compress_code_video_stream.sh" "$RTSP_URL" "$CAPTURE_DURATION" "$TIMESTAMP" &
COMPRESS_PID=$!

"$SCRIPT_DIR/save_rtsp_raw_stream.sh" "$RTSP_URL" "$CAPTURE_DURATION" "$TIMESTAMP" &
RAW_PID=$!

# 🧘 Wait for both to complete
wait $COMPRESS_PID
wait $RAW_PID

log "✅ Both capture processes completed."

# 🎬 Convert .ts files to MP4 and store locally
convert_to_mp4_folder "$RAW_DIR" "original"
convert_to_mp4_folder "$ENCODED_DIR" "compressed"

log "🏁 All done."

