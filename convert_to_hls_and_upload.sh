#!/bin/bash
# ─────────────────────────────────────────────
# Script: convert_to_hls_and_upload.sh
# Purpose:
#   - Convert .ts file to HLS format (.m3u8 + .ts segments)
#   - Upload HLS files to S3 bucket
#   - Provide web-accessible streaming URLs
# ─────────────────────────────────────────────

# 📥 Parse input arguments
TS_FILE="$1"
S3_BUCKET="$2"
S3_PREFIX="$3"  # Optional prefix for S3 path

if [ -z "$TS_FILE" ] || [ -z "$S3_BUCKET" ]; then
    echo "❗ Usage: $0 <TS_FILE> <S3_BUCKET> [S3_PREFIX]"
    echo "   Example: $0 original/stream/stream_20241217_143022.ts my-streaming-bucket streams/"
    exit 1
fi

# ✅ Check if input file exists
if [ ! -f "$TS_FILE" ]; then
    echo "❌ Input file not found: $TS_FILE"
    exit 1
fi

# 📂 Create output directories
BASENAME=$(basename "$TS_FILE" .ts)
HLS_DIR="hls/${BASENAME}"
mkdir -p "$HLS_DIR"
mkdir -p logs

# 🕒 Timestamp for logging
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="logs/hls_conversion_${TIMESTAMP}.log"

echo "🎬 Converting .ts to HLS format..." | tee -a "$LOG_FILE"
echo "📁 Input: $TS_FILE" | tee -a "$LOG_FILE"
echo "📁 Output: $HLS_DIR" | tee -a "$LOG_FILE"

# 🔄 Convert to HLS using ffmpeg
# -hls_time: segment duration in seconds
# -hls_list_size: maximum number of segments in playlist (0 = unlimited)
# -hls_segment_filename: pattern for segment files
ffmpeg -y -hide_banner -loglevel info \
    -i "$TS_FILE" \
    -c copy \
    -hls_time 10 \
    -hls_list_size 0 \
    -hls_segment_filename "${HLS_DIR}/segment_%03d.ts" \
    "${HLS_DIR}/playlist.m3u8" 2>> "$LOG_FILE"

# ✅ Check conversion success
if [ $? -ne 0 ]; then
    echo "❌ HLS conversion failed. Check log: $LOG_FILE" | tee -a "$LOG_FILE"
    exit 1
fi

echo "✅ HLS conversion completed" | tee -a "$LOG_FILE"

# 📤 Upload to S3
echo "☁️ Uploading HLS files to S3..." | tee -a "$LOG_FILE"

# Set S3 path
if [ -n "$S3_PREFIX" ]; then
    S3_PATH="s3://${S3_BUCKET}/${S3_PREFIX}${BASENAME}/"
else
    S3_PATH="s3://${S3_BUCKET}/${BASENAME}/"
fi

echo "📤 S3 destination: $S3_PATH" | tee -a "$LOG_FILE"

# Upload all HLS files with proper content types
aws s3 sync "$HLS_DIR/" "$S3_PATH" \
    --content-type-by-extension \
    --metadata-directive REPLACE \
    --cache-control "max-age=3600" 2>> "$LOG_FILE"

# Set specific content type for .m3u8 files
aws s3 cp "${HLS_DIR}/playlist.m3u8" "${S3_PATH}playlist.m3u8" \
    --content-type "application/vnd.apple.mpegurl" \
    --cache-control "max-age=60" 2>> "$LOG_FILE"

# ✅ Check upload success
if [ $? -eq 0 ]; then
    echo "✅ Upload completed successfully" | tee -a "$LOG_FILE"
    
    # 🌐 Generate web-accessible URLs
    if [ -n "$S3_PREFIX" ]; then
        PLAYLIST_URL="https://${S3_BUCKET}.s3.amazonaws.com/${S3_PREFIX}${BASENAME}/playlist.m3u8"
    else
        PLAYLIST_URL="https://${S3_BUCKET}.s3.amazonaws.com/${BASENAME}/playlist.m3u8"
    fi
    
    echo "" | tee -a "$LOG_FILE"
    echo "🎯 HLS Playlist URL:" | tee -a "$LOG_FILE"
    echo "$PLAYLIST_URL" | tee -a "$LOG_FILE"
    echo "" | tee -a "$LOG_FILE"
    echo "📋 Use this URL in your web video players (HLS.js, Video.js, etc.)" | tee -a "$LOG_FILE"
    
    # Save URL to a file for easy access
    echo "$PLAYLIST_URL" > "${HLS_DIR}/stream_url.txt"
    
else
    echo "❌ S3 upload failed. Check AWS credentials and permissions." | tee -a "$LOG_FILE"
    exit 1
fi

echo "🎉 Process completed! HLS stream ready for web viewing." | tee -a "$LOG_FILE"
