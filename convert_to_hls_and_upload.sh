#!/bin/bash
# ─────────────────────────────────────────────
# Script: convert_to_hls_and_upload.sh
# Purpose:
#   - Convert .ts file to HLS format (.m3u8 + .ts segments)
#   - Upload HLS files to S3 bucket
#   - Provide web-accessible streaming URLs
# ─────────────────────────────────────────────

# 📥 Parse input arguments
TS_FILE="$1"
S3_BUCKET="$2"
S3_PREFIX="$3"  # Optional prefix for S3 path
SESSION_ID="$4" # Optional session ID for web dashboard integration

if [ -z "$TS_FILE" ] || [ -z "$S3_BUCKET" ]; then
    echo "❗ Usage: $0 <TS_FILE> <S3_BUCKET> [S3_PREFIX] [SESSION_ID]"
    echo "   Example: $0 original/stream/stream_20241217_143022.ts my-streaming-bucket streams/ session123"
    exit 1
fi

# ✅ Check if input file exists
if [ ! -f "$TS_FILE" ]; then
    echo "❌ Input file not found: $TS_FILE"
    exit 1
fi

# 📂 Create output directories
BASENAME=$(basename "$TS_FILE" .ts)
HLS_DIR="hls/${BASENAME}"
mkdir -p "$HLS_DIR"
mkdir -p logs

# 🕒 Timestamp for logging
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="logs/hls_conversion_${TIMESTAMP}.log"

echo "🎬 Converting .ts to HLS format..." | tee -a "$LOG_FILE"
echo "📁 Input: $TS_FILE" | tee -a "$LOG_FILE"
echo "📁 Output: $HLS_DIR" | tee -a "$LOG_FILE"

# 🔄 Convert to HLS using ffmpeg
# -hls_time: segment duration in seconds
# -hls_list_size: maximum number of segments in playlist (0 = unlimited)
# -hls_segment_filename: pattern for segment files
ffmpeg -y -hide_banner -loglevel info \
    -i "$TS_FILE" \
    -c copy \
    -hls_time 10 \
    -hls_list_size 0 \
    -hls_segment_filename "${HLS_DIR}/segment_%03d.ts" \
    "${HLS_DIR}/playlist.m3u8" 2>> "$LOG_FILE"

# ✅ Check conversion success
if [ $? -ne 0 ]; then
    echo "❌ HLS conversion failed. Check log: $LOG_FILE" | tee -a "$LOG_FILE"
    exit 1
fi

echo "✅ HLS conversion completed" | tee -a "$LOG_FILE"

# 📤 Upload to S3
echo "☁️ Uploading HLS files to S3..." | tee -a "$LOG_FILE"

# Notify web dashboard that upload is starting (show live stream container)
if [ -n "$SESSION_ID" ]; then
    node notify_websocket.js upload_started "$SESSION_ID" 2>/dev/null || echo "WebSocket notification failed (server may not be running)"
fi

# Set S3 path
if [ -n "$S3_PREFIX" ]; then
    S3_PATH="s3://${S3_BUCKET}/${S3_PREFIX}${BASENAME}/"
else
    S3_PATH="s3://${S3_BUCKET}/${BASENAME}/"
fi

echo "📤 S3 destination: $S3_PATH" | tee -a "$LOG_FILE"

# Upload all HLS files with proper content types
aws s3 sync "$HLS_DIR/" "$S3_PATH" \
    --content-type-by-extension \
    --metadata-directive REPLACE \
    --cache-control "max-age=3600" 2>> "$LOG_FILE"

# Set specific content type for .m3u8 files
aws s3 cp "${HLS_DIR}/playlist.m3u8" "${S3_PATH}playlist.m3u8" \
    --content-type "application/vnd.apple.mpegurl" \
    --cache-control "max-age=60" 2>> "$LOG_FILE"

# ✅ Check upload success
if [ $? -eq 0 ]; then
    echo "✅ Upload completed successfully" | tee -a "$LOG_FILE"

    # 🔐 Generate pre-signed URLs for private S3 access
    echo "🔐 Generating pre-signed URLs..." | tee -a "$LOG_FILE"

    # Set expiration time (24 hours = 86400 seconds)
    EXPIRATION_TIME=86400

    # Generate pre-signed URL for playlist
    if [ -n "$S3_PREFIX" ]; then
        PLAYLIST_S3_KEY="${S3_PREFIX}${BASENAME}/playlist.m3u8"
    else
        PLAYLIST_S3_KEY="${BASENAME}/playlist.m3u8"
    fi

    PRESIGNED_PLAYLIST_URL=$(aws s3 presign "s3://${S3_BUCKET}/${PLAYLIST_S3_KEY}" --expires-in "$EXPIRATION_TIME" 2>> "$LOG_FILE")

    if [ $? -eq 0 ] && [ -n "$PRESIGNED_PLAYLIST_URL" ]; then
        echo "✅ Pre-signed playlist URL generated" | tee -a "$LOG_FILE"

        # Create modified playlist with pre-signed segment URLs
        PRESIGNED_PLAYLIST="${HLS_DIR}/playlist_presigned.m3u8"

        echo "🔄 Creating playlist with pre-signed segment URLs..." | tee -a "$LOG_FILE"

        # Read original playlist and replace segment URLs with pre-signed URLs
        while IFS= read -r line; do
            if [[ "$line" == segment_*.ts ]]; then
                # Generate pre-signed URL for this segment
                if [ -n "$S3_PREFIX" ]; then
                    SEGMENT_S3_KEY="${S3_PREFIX}${BASENAME}/${line}"
                else
                    SEGMENT_S3_KEY="${BASENAME}/${line}"
                fi

                PRESIGNED_SEGMENT_URL=$(aws s3 presign "s3://${S3_BUCKET}/${SEGMENT_S3_KEY}" --expires-in "$EXPIRATION_TIME" 2>> "$LOG_FILE")

                if [ $? -eq 0 ] && [ -n "$PRESIGNED_SEGMENT_URL" ]; then
                    echo "$PRESIGNED_SEGMENT_URL" >> "$PRESIGNED_PLAYLIST"
                else
                    echo "⚠️ Failed to generate pre-signed URL for segment: $line" | tee -a "$LOG_FILE"
                    echo "$line" >> "$PRESIGNED_PLAYLIST"
                fi
            else
                echo "$line" >> "$PRESIGNED_PLAYLIST"
            fi
        done < "${HLS_DIR}/playlist.m3u8"

        # Upload the pre-signed playlist
        aws s3 cp "$PRESIGNED_PLAYLIST" "${S3_PATH}playlist_presigned.m3u8" \
            --content-type "application/vnd.apple.mpegurl" \
            --cache-control "max-age=60" 2>> "$LOG_FILE"

        # Use pre-signed playlist URL for web access
        if [ -n "$S3_PREFIX" ]; then
            PRESIGNED_PLAYLIST_S3_KEY="${S3_PREFIX}${BASENAME}/playlist_presigned.m3u8"
        else
            PRESIGNED_PLAYLIST_S3_KEY="${BASENAME}/playlist_presigned.m3u8"
        fi

        FINAL_PLAYLIST_URL=$(aws s3 presign "s3://${S3_BUCKET}/${PRESIGNED_PLAYLIST_S3_KEY}" --expires-in "$EXPIRATION_TIME" 2>> "$LOG_FILE")

        echo "" | tee -a "$LOG_FILE"
        echo "🎯 Pre-signed HLS Playlist URL:" | tee -a "$LOG_FILE"
        echo "$FINAL_PLAYLIST_URL" | tee -a "$LOG_FILE"
        echo "" | tee -a "$LOG_FILE"
        echo "📋 This URL works with private S3 buckets (expires in 24 hours)" | tee -a "$LOG_FILE"

        # Save pre-signed URL to files
        echo "$FINAL_PLAYLIST_URL" > "${HLS_DIR}/stream_url.txt"

        # If SESSION_ID is provided, also save for web dashboard integration
        if [ -n "$SESSION_ID" ]; then
            WEB_HLS_DIR="hls/${SESSION_ID}/${BASENAME}"
            mkdir -p "$WEB_HLS_DIR"
            echo "$FINAL_PLAYLIST_URL" > "${WEB_HLS_DIR}/stream_url.txt"
            echo "📱 Web dashboard integration: ${WEB_HLS_DIR}/stream_url.txt" | tee -a "$LOG_FILE"

            # Notify web dashboard that HLS is ready
            node notify_websocket.js hls_ready "$SESSION_ID" "{\"url\":\"$FINAL_PLAYLIST_URL\",\"filename\":\"$filename\"}" 2>/dev/null || echo "WebSocket notification failed"
        fi

    else
        echo "⚠️ Failed to generate pre-signed URLs, using public URLs" | tee -a "$LOG_FILE"

        # Fallback to public URLs
        if [ -n "$S3_PREFIX" ]; then
            PLAYLIST_URL="https://${S3_BUCKET}.s3.amazonaws.com/${S3_PREFIX}${BASENAME}/playlist.m3u8"
        else
            PLAYLIST_URL="https://${S3_BUCKET}.s3.amazonaws.com/${BASENAME}/playlist.m3u8"
        fi

        echo "$PLAYLIST_URL" > "${HLS_DIR}/stream_url.txt"

        if [ -n "$SESSION_ID" ]; then
            WEB_HLS_DIR="hls/${SESSION_ID}/${BASENAME}"
            mkdir -p "$WEB_HLS_DIR"
            echo "$PLAYLIST_URL" > "${WEB_HLS_DIR}/stream_url.txt"
        fi
    fi

else
    echo "❌ S3 upload failed. Check AWS credentials and permissions." | tee -a "$LOG_FILE"
    exit 1
fi

echo "🎉 Process completed! HLS stream ready for web viewing." | tee -a "$LOG_FILE"
