🎬 Converting .ts to .mp4 format...
📁 Input: /home/<USER>/Documents/augment-projects/zmt-live/original/stream/stream_20250617_105528.ts
📁 Output: mp4/original.mp4 (original stream)
Input #0, mpegts, from '/home/<USER>/Documents/augment-projects/zmt-live/original/stream/stream_20250617_105528.ts':
  Duration: 00:00:59.67, start: 1.765633, bitrate: 1137 kb/s
  Program 1 
    Metadata:
      service_name    : Stream
      service_provider: FFmpeg
  Stream #0:0[0x100]: Video: h264 (High) ([27][0][0][0] / 0x001B), yuvj420p(pc, bt709, progressive), 1024x768 [SAR 128:129 DAR 512:387], 30 fps, 29.97 tbr, 90k tbn
Output #0, mp4, to 'mp4/original.mp4':
  Metadata:
    encoder         : Lavf60.16.100
  Stream #0:0: Video: h264 (High) (avc1 / 0x31637661), yuvj420p(pc, bt709, progressive), 1024x768 [SAR 128:129 DAR 512:387], q=2-31, 30 fps, 29.97 tbr, 90k tbn
Stream mapping:
  Stream #0:0 -> #0:0 (copy)
Press [q] to stop, [?] for help
size=       0kB time=00:00:00.00 bitrate=N/A speed=N/A    
[out#0/mp4 @ 0x577994053700] video:7685kB audio:0kB subtitle:0kB other streams:0kB global headers:0kB muxing overhead: 0.228753%
size=    7703kB time=00:00:59.63 bitrate=1058.1kbits/s speed=3.93e+03x    
✅ MP4 conversion completed
💾 Keeping MP4 file locally for web serving...
✅ MP4 file ready for local serving

🎯 Local MP4 Video URL:
/mp4/original.mp4

📋 Use this URL in your web video players
📱 Web dashboard integration: mp4/20250617_105528/original_url.txt
🎉 Process completed! MP4 video ready for local web serving.
