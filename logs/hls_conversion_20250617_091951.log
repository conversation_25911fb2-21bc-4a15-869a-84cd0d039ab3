🎬 Converting .ts to HLS format...
📁 Input: original/stream/stream_fixes_20250617_091942.ts
📁 Output: hls/stream_fixes_20250617_091942
Input #0, mov,mp4,m4a,3gp,3g2,mj2, from 'original/stream/stream_fixes_20250617_091942.ts':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    encoder         : Lavf60.16.100
  Duration: 00:00:30.00, start: 0.000000, bitrate: 79 kb/s
  Stream #0:0[0x1](und): Video: h264 (High 4:4:4 Predictive) (avc1 / 0x31637661), yuv444p(progressive), 320x240 [SAR 1:1 DAR 4:3], 8 kb/s, 1 fps, 1 tbr, 16384 tbn (default)
    Metadata:
      handler_name    : VideoHandler
      vendor_id       : [0][0][0][0]
      encoder         : Lavc60.31.102 libx264
  Stream #0:1[0x2](und): Audio: aac (LC) (mp4a / 0x6134706D), 44100 Hz, mono, fltp, 69 kb/s (default)
    Metadata:
      handler_name    : SoundHandler
      vendor_id       : [0][0][0][0]
Output #0, hls, to 'hls/stream_fixes_20250617_091942/playlist.m3u8':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    encoder         : Lavf60.16.100
  Stream #0:0(und): Video: h264 (High 4:4:4 Predictive) (avc1 / 0x31637661), yuv444p(progressive), 320x240 [SAR 1:1 DAR 4:3], q=2-31, 8 kb/s, 1 fps, 1 tbr, 90k tbn (default)
    Metadata:
      handler_name    : VideoHandler
      vendor_id       : [0][0][0][0]
      encoder         : Lavc60.31.102 libx264
  Stream #0:1(und): Audio: aac (LC) (mp4a / 0x6134706D), 44100 Hz, mono, fltp, 69 kb/s (default)
    Metadata:
      handler_name    : SoundHandler
      vendor_id       : [0][0][0][0]
Stream mapping:
  Stream #0:0 -> #0:0 (copy)
  Stream #0:1 -> #0:1 (copy)
Press [q] to stop, [?] for help
size=       0kB time=-00:00:02.00 bitrate=  -0.0kbits/s speed=N/A    
[hls @ 0x5a1503e6a8c0] Opening 'hls/stream_fixes_20250617_091942/segment_000.ts' for writing
[hls @ 0x5a1503e6a8c0] Opening 'hls/stream_fixes_20250617_091942/playlist.m3u8.tmp' for writing
[out#0/hls @ 0x5a1503e6af40] video:31kB audio:254kB subtitle:0kB other streams:0kB global headers:0kB muxing overhead: unknown
size=N/A time=00:00:29.97 bitrate=N/A speed=5.12e+03x    
✅ HLS conversion completed
☁️ Uploading HLS files to S3...
📤 S3 destination: s3://zmt-live-test-streaming-1750164773/test-fixes/stream_fixes_20250617_091942/

Unknown options: --content-type-by-extension
✅ Upload completed successfully
🔐 Generating pre-signed URLs...
🌍 Bucket region: us-west-1
✅ Pre-signed playlist URL generated
🔄 Creating playlist with pre-signed segment URLs...

🎯 Pre-signed HLS Playlist URL:
https://zmt-live-test-streaming-1750164773.s3.us-west-1.amazonaws.com/test-fixes/stream_fixes_20250617_091942/playlist_presigned.m3u8?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIARWTHWVS7NV2YMDHW%2F20250617%2Fus-west-1%2Fs3%2Faws4_request&X-Amz-Date=20250617T131956Z&X-Amz-Expires=86400&X-Amz-SignedHeaders=host&X-Amz-Signature=abbd7c899ebd81ef323a2b2d493cd3103548af7c591c664facf07f9d1f6b2d96

📋 This URL works with private S3 buckets (expires in 24 hours)
📱 Web dashboard integration: hls/test_fixes_1750166391/stream_fixes_20250617_091942/stream_url.txt
🎉 Process completed! HLS stream ready for web viewing.
