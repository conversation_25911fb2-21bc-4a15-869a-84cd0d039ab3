🎬 Converting .ts to .mp4 format...
📁 Input: original/stream/stream_20250617_100453.ts
📁 Output: mp4/original.mp4 (original stream)
Input #0, mov,mp4,m4a,3gp,3g2,mj2, from 'original/stream/stream_20250617_100453.ts':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    encoder         : Lavf60.16.100
  Duration: 00:00:10.00, start: 0.000000, bitrate: 95 kb/s
  Stream #0:0[0x1](und): Video: h264 (High 4:4:4 Predictive) (avc1 / 0x31637661), yuv444p(progressive), 640x480 [SAR 1:1 DAR 4:3], 22 kb/s, 2 fps, 2 tbr, 16384 tbn (default)
    Metadata:
      handler_name    : VideoHandler
      vendor_id       : [0][0][0][0]
      encoder         : Lavc60.31.102 libx264
  Stream #0:1[0x2](und): Audio: aac (LC) (mp4a / 0x6134706D), 44100 Hz, mono, fltp, 69 kb/s (default)
    Metadata:
      handler_name    : SoundHandler
      vendor_id       : [0][0][0][0]
Output #0, mp4, to 'mp4/original.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    encoder         : Lavf60.16.100
  Stream #0:0(und): Video: h264 (High 4:4:4 Predictive) (avc1 / 0x31637661), yuv444p(progressive), 640x480 [SAR 1:1 DAR 4:3], q=2-31, 22 kb/s, 2 fps, 2 tbr, 16384 tbn (default)
    Metadata:
      handler_name    : VideoHandler
      vendor_id       : [0][0][0][0]
      encoder         : Lavc60.31.102 libx264
  Stream #0:1(und): Audio: aac (LC) (mp4a / 0x6134706D), 44100 Hz, mono, fltp, 69 kb/s (default)
    Metadata:
      handler_name    : SoundHandler
      vendor_id       : [0][0][0][0]
Stream mapping:
  Stream #0:0 -> #0:0 (copy)
  Stream #0:1 -> #0:1 (copy)
Press [q] to stop, [?] for help
size=       0kB time=-00:00:01.00 bitrate=  -0.0kbits/s speed=N/A    
[out#0/mp4 @ 0x611beb4a3c00] video:28kB audio:85kB subtitle:0kB other streams:0kB global headers:0kB muxing overhead: 3.238947%
size=     116kB time=00:00:09.98 bitrate=  95.4kbits/s speed=5.84e+03x    
✅ MP4 conversion completed
☁️ Uploading MP4 file to S3...
🌍 Bucket region: us-west-1
📤 S3 destination: s3://zmt-live-test-streaming-1750164773/mp4-test/original.mp4
✅ Upload completed successfully

🎯 MP4 Video URL:
https://zmt-live-test-streaming-1750164773.s3.amazonaws.com/mp4-test/original.mp4

📋 Use this URL in your web video players
📱 Web dashboard integration: mp4/mp4_test_1750169093/original_url.txt
🎉 Process completed! MP4 video ready for web viewing.
