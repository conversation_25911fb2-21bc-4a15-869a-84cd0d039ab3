#!/bin/bash
# ─────────────────────────────────────────────
# Script: post_process_stream.sh
# Purpose:
#   - Called after RTSP stream capture completes
#   - Converts the captured .ts file to HLS format
#   - Uploads to S3 for web streaming
#   - Integrates with web dashboard
# ─────────────────────────────────────────────

# 📥 Parse input arguments
TS_FILE="$1"
SESSION_ID="$2"

if [ -z "$TS_FILE" ]; then
    echo "❗ Usage: $0 <TS_FILE> [SESSION_ID]"
    echo "   Example: $0 original/stream/stream_20241217_143022.ts session123"
    exit 1
fi

# 📋 Configuration from environment or defaults
S3_BUCKET="${S3_BUCKET:-your-streaming-bucket}"
S3_PREFIX="${S3_PREFIX:-streams/}"

# ✅ Check if input file exists
if [ ! -f "$TS_FILE" ]; then
    echo "❌ Input file not found: $TS_FILE"
    exit 1
fi

# 🆔 Extract session ID from filename if not provided
if [ -z "$SESSION_ID" ]; then
    BASENAME=$(basename "$TS_FILE")
    if [[ "$BASENAME" =~ stream_([0-9]{8}_[0-9]{6})\.ts ]]; then
        SESSION_ID="${BASH_REMATCH[1]}"
        echo "🆔 Extracted session ID: $SESSION_ID"
    fi
fi

echo "🎬 Post-processing stream file..."
echo "📁 Input: $TS_FILE"
echo "🆔 Session: ${SESSION_ID:-none}"
echo "☁️ S3 Bucket: $S3_BUCKET"
echo ""

# 🔄 Convert to MP4 and upload
echo "🚀 Starting MP4 conversion and S3 upload..."
if ./convert_to_mp4_and_upload.sh "$TS_FILE" "$S3_BUCKET" "$S3_PREFIX" "$SESSION_ID"; then
    echo "✅ Post-processing completed successfully!"

    # 📱 If session ID is available, notify about web dashboard availability
    if [ -n "$SESSION_ID" ]; then
        echo ""
        echo "🌐 Stream is now available in the web dashboard"
        echo "📺 Session ID: $SESSION_ID"
        echo "🔗 The compressed stream will automatically load MP4 from S3"
    fi

    exit 0
else
    echo "❌ Post-processing failed!"
    exit 1
fi
