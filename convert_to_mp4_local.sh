#!/bin/bash
# ─────────────────────────────────────────────
# Script: convert_to_mp4_local.sh
# Purpose:
#   - Convert .ts file to .mp4 format
#   - Keep MP4 files locally for web serving
#   - Use simple names without timestamps to save space
# ─────────────────────────────────────────────

# 📥 Parse input arguments
TS_FILE="$1"
SESSION_ID="$2" # Optional session ID for web dashboard integration

if [ -z "$TS_FILE" ]; then
    echo "❗ Usage: $0 <TS_FILE> [SESSION_ID]"
    echo "   Example: $0 original/stream/stream_123.ts session123"
    echo "   Example: $0 encoded/stream/stream_123.ts session123"
    exit 1
fi

# ✅ Check if input file exists
if [ ! -f "$TS_FILE" ]; then
    echo "❌ Input file not found: $TS_FILE"
    exit 1
fi

# 📂 Create output directories
MP4_DIR="mp4"
mkdir -p "$MP4_DIR"
mkdir -p logs

# 🕒 Timestamp for logging
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="logs/mp4_conversion_${TIMESTAMP}.log"

echo "🎬 Converting .ts to .mp4 format..." | tee -a "$LOG_FILE"
echo "📁 Input: $TS_FILE" | tee -a "$LOG_FILE"

# 🔄 Determine output file name based on input path
if [[ "$TS_FILE" == *"original"* ]]; then
    OUTPUT_MP4="${MP4_DIR}/original.mp4"
    S3_FILENAME="original.mp4"
    echo "📁 Output: $OUTPUT_MP4 (original stream)" | tee -a "$LOG_FILE"
else
    OUTPUT_MP4="${MP4_DIR}/compressed.mp4"
    S3_FILENAME="compressed.mp4"
    echo "📁 Output: $OUTPUT_MP4 (compressed stream)" | tee -a "$LOG_FILE"
fi

# 🔄 Convert to MP4 using ffmpeg (simple copy, no re-encoding)
ffmpeg -y -hide_banner -loglevel info \
    -i "$TS_FILE" \
    -c copy \
    "$OUTPUT_MP4" 2>> "$LOG_FILE"

# ✅ Check conversion success
if [ $? -ne 0 ]; then
    echo "❌ MP4 conversion failed. Check log: $LOG_FILE" | tee -a "$LOG_FILE"
    exit 1
fi

echo "✅ MP4 conversion completed" | tee -a "$LOG_FILE"

# 📁 Keep MP4 locally for web serving
echo "💾 Keeping MP4 file locally for web serving..." | tee -a "$LOG_FILE"

# Notify web dashboard that processing is starting (show live stream container)
if [ -n "$SESSION_ID" ]; then
    node notify_websocket.js upload_started "$SESSION_ID" 2>/dev/null || echo "WebSocket notification failed (server may not be running)"
fi

# Create local URL for web serving
LOCAL_URL="/mp4/${S3_FILENAME}"

echo "✅ MP4 file ready for local serving" | tee -a "$LOG_FILE"
echo "" | tee -a "$LOG_FILE"
echo "🎯 Local MP4 Video URL:" | tee -a "$LOG_FILE"
echo "$LOCAL_URL" | tee -a "$LOG_FILE"
echo "" | tee -a "$LOG_FILE"
echo "📋 Use this URL in your web video players" | tee -a "$LOG_FILE"

# Save URL to a file for easy access
echo "$LOCAL_URL" > "${MP4_DIR}/${S3_FILENAME%.mp4}_url.txt"

# If SESSION_ID is provided, also save for web dashboard integration
if [ -n "$SESSION_ID" ]; then
    WEB_MP4_DIR="mp4/${SESSION_ID}"
    mkdir -p "$WEB_MP4_DIR"
    echo "$LOCAL_URL" > "${WEB_MP4_DIR}/${S3_FILENAME%.mp4}_url.txt"
    echo "📱 Web dashboard integration: ${WEB_MP4_DIR}/${S3_FILENAME%.mp4}_url.txt" | tee -a "$LOG_FILE"

    # Notify web dashboard that MP4 is ready
    node notify_websocket.js mp4_ready "$SESSION_ID" "{\"url\":\"$LOCAL_URL\",\"filename\":\"$S3_FILENAME\",\"type\":\"${S3_FILENAME%.mp4}\"}" 2>/dev/null || echo "WebSocket notification failed"
fi

echo "🎉 Process completed! MP4 video ready for local web serving." | tee -a "$LOG_FILE"
