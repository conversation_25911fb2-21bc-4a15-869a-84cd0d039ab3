#!/usr/bin/env node
// ─────────────────────────────────────────────
// Script: notify_websocket.js
// Purpose:
//   - Send WebSocket notifications to the dashboard
//   - Used by shell scripts to communicate with web interface
// ─────────────────────────────────────────────

const WebSocket = require('ws');

// Parse command line arguments
const args = process.argv.slice(2);
if (args.length < 2) {
    console.error('Usage: node notify_websocket.js <message_type> <session_id> [data]');
    console.error('Examples:');
    console.error('  node notify_websocket.js upload_started session123');
    console.error('  node notify_websocket.js hls_ready session123 \'{"url":"https://..."}\'');
    process.exit(1);
}

const messageType = args[0];
const sessionId = args[1];
const data = args[2] ? JSON.parse(args[2]) : {};

// WebSocket server URL
const wsUrl = 'ws://localhost:8080';

// Create WebSocket connection
const ws = new WebSocket(wsUrl);

ws.on('open', () => {
    const message = {
        type: messageType,
        sessionId: sessionId,
        data: {
            ...data,
            timestamp: new Date().toISOString()
        }
    };
    
    console.log(`Sending WebSocket message: ${JSON.stringify(message)}`);
    ws.send(JSON.stringify(message));
    
    // Close connection after sending
    setTimeout(() => {
        ws.close();
        process.exit(0);
    }, 100);
});

ws.on('error', (error) => {
    console.error('WebSocket error:', error.message);
    process.exit(1);
});

ws.on('close', () => {
    console.log('WebSocket connection closed');
});
