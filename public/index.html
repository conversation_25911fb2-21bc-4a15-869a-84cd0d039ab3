<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RTSP Compression Dashboard</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
</head>
<body>
    <div class="container">
        <header>
            <h1><i class="fas fa-video"></i> RTSP Compression Dashboard</h1>
            <p>Live stream compression with S3 upload and bandwidth analysis</p>
        </header>

        <main>
            <!-- Input Form -->
            <section class="input-section">
                <div class="card">
                    <h2><i class="fas fa-cog"></i> Configuration</h2>
                    <form id="compressionForm">
                        <div class="form-group">
                            <label for="rtspUrl">
                                <i class="fas fa-link"></i> RTSP Stream URL
                            </label>
                            <input 
                                type="text" 
                                id="rtspUrl" 
                                placeholder="rtsp://your.camera.ip:554/stream1"
                                required
                            >
                            <small>Enter the RTSP URL of your video stream</small>
                        </div>

                        <div class="form-group">
                            <label for="duration">
                                <i class="fas fa-clock"></i> Duration (minutes)
                            </label>
                            <input 
                                type="number" 
                                id="duration" 
                                min="1" 
                                max="15" 
                                value="3"
                                required
                            >
                            <small>Duration between 1-15 minutes (max 15 for safety)</small>
                        </div>

                        <button type="submit" id="startBtn" class="btn btn-primary">
                            <i class="fas fa-play"></i> Start Compression
                        </button>
                        
                        <button type="button" id="stopBtn" class="btn btn-danger" style="display: none;">
                            <i class="fas fa-stop"></i> Stop Process
                        </button>
                    </form>
                </div>
            </section>

            <!-- Status Section -->
            <section class="status-section">
                <div class="card">
                    <h2><i class="fas fa-info-circle"></i> Status</h2>
                    <div id="statusDisplay" class="status-display">
                        <div class="status-item">
                            <span class="status-label">Status:</span>
                            <span id="currentStatus" class="status-value">Ready</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">Session ID:</span>
                            <span id="sessionId" class="status-value">-</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">Duration:</span>
                            <span id="configuredDuration" class="status-value">-</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Live Stream Display -->
            <section class="stream-section" style="display: none;">
                <div class="card">
                    <h2><i class="fas fa-video"></i> Live Streams</h2>
                    <div class="streams-container">
                        <div class="stream-box">
                            <h3><i class="fas fa-broadcast-tower"></i> Live RTSP Stream</h3>
                            <div class="video-container">
                                <video id="liveStream" controls muted autoplay playsinline>
                                    Your browser does not support the video tag.
                                </video>
                                <div class="stream-overlay" id="liveStreamOverlay">
                                    <i class="fas fa-spinner fa-spin"></i>
                                    <p>Connecting to live stream...</p>
                                </div>
                            </div>
                        </div>

                        <div class="stream-box">
                            <h3><i class="fas fa-compress-alt"></i> Compressed Stream</h3>
                            <div class="video-container">
                                <video id="compressedStream" controls muted playsinline>
                                    Your browser does not support the video tag.
                                </video>
                                <div class="stream-overlay" id="compressedStreamOverlay">
                                    <i class="fas fa-clock"></i>
                                    <p>Waiting for compressed data...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Real-time Metrics -->
            <section class="metrics-section">
                <div class="card">
                    <h2><i class="fas fa-chart-line"></i> Real-time Metrics</h2>
                    <div class="metrics-grid">
                        <div class="metric-card">
                            <div class="metric-icon">
                                <i class="fas fa-file-video"></i>
                            </div>
                            <div class="metric-content">
                                <h3>Raw Stream</h3>
                                <div class="metric-value" id="rawSize">0 Bytes</div>
                                <div class="metric-label">Original file size</div>
                            </div>
                        </div>

                        <div class="metric-card">
                            <div class="metric-icon">
                                <i class="fas fa-compress-alt"></i>
                            </div>
                            <div class="metric-content">
                                <h3>Compressed</h3>
                                <div class="metric-value" id="compressedSize">0 Bytes</div>
                                <div class="metric-label">Compressed file size</div>
                            </div>
                        </div>

                        <div class="metric-card">
                            <div class="metric-icon">
                                <i class="fas fa-percentage"></i>
                            </div>
                            <div class="metric-content">
                                <h3>Compression</h3>
                                <div class="metric-value" id="compressionRatio">0%</div>
                                <div class="metric-label">Space saved</div>
                            </div>
                        </div>

                        <div class="metric-card">
                            <div class="metric-icon">
                                <i class="fas fa-wifi"></i>
                            </div>
                            <div class="metric-content">
                                <h3>Bandwidth</h3>
                                <div class="metric-value" id="bandwidthSavings">0%</div>
                                <div class="metric-label">Bandwidth saved</div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Progress Bar -->
            <section class="progress-section" style="display: none;">
                <div class="card">
                    <h2><i class="fas fa-tasks"></i> Progress</h2>
                    <div class="progress-container">
                        <div class="progress-bar">
                            <div id="progressFill" class="progress-fill"></div>
                        </div>
                        <div class="progress-text">
                            <span id="progressPercent">0%</span>
                            <span id="timeRemaining">Estimating...</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Logs Section -->
            <section class="logs-section">
                <div class="card">
                    <h2><i class="fas fa-terminal"></i> Process Logs</h2>
                    <div id="logsContainer" class="logs-container">
                        <div class="log-entry info">
                            <span class="log-time">[Ready]</span>
                            <span class="log-message">System ready. Enter RTSP URL and duration to start.</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Final Report -->
            <section class="report-section" style="display: none;">
                <div class="card">
                    <h2><i class="fas fa-chart-bar"></i> Final Report</h2>
                    <div id="finalReport" class="final-report">
                        <!-- Report content will be populated by JavaScript -->
                    </div>
                </div>
            </section>
        </main>
    </div>

    <script src="app.js"></script>
</body>
</html>
