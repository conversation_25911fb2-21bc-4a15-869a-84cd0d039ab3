class RTSPDashboard {
    constructor() {
        this.ws = null;
        this.currentSessionId = null;
        this.startTime = null;
        this.estimatedDuration = 0;
        this.progressInterval = null;
        
        this.initializeElements();
        this.bindEvents();
        this.connectWebSocket();
    }

    initializeElements() {
        // Form elements
        this.form = document.getElementById('compressionForm');
        this.rtspUrlInput = document.getElementById('rtspUrl');
        this.durationInput = document.getElementById('duration');
        this.startBtn = document.getElementById('startBtn');
        this.stopBtn = document.getElementById('stopBtn');

        // Status elements
        this.currentStatus = document.getElementById('currentStatus');
        this.sessionIdDisplay = document.getElementById('sessionId');
        this.configuredDuration = document.getElementById('configuredDuration');

        // Metrics elements
        this.rawSize = document.getElementById('rawSize');
        this.compressedSize = document.getElementById('compressedSize');
        this.compressionRatio = document.getElementById('compressionRatio');
        this.bandwidthSavings = document.getElementById('bandwidthSavings');

        // Progress elements
        this.progressSection = document.querySelector('.progress-section');
        this.progressFill = document.getElementById('progressFill');
        this.progressPercent = document.getElementById('progressPercent');
        this.timeRemaining = document.getElementById('timeRemaining');

        // Logs and report
        this.logsContainer = document.getElementById('logsContainer');
        this.reportSection = document.querySelector('.report-section');
        this.finalReport = document.getElementById('finalReport');

        // Stream elements
        this.streamSection = document.querySelector('.stream-section');
        this.liveStream = document.getElementById('liveStream');
        this.compressedStream = document.getElementById('compressedStream');
        this.liveStreamOverlay = document.getElementById('liveStreamOverlay');
        this.compressedStreamOverlay = document.getElementById('compressedStreamOverlay');
    }

    bindEvents() {
        this.form.addEventListener('submit', (e) => this.handleStartCompression(e));
        this.stopBtn.addEventListener('click', () => this.handleStopCompression());
    }

    connectWebSocket() {
        const wsUrl = `ws://${window.location.hostname}:8080`;
        this.ws = new WebSocket(wsUrl);

        this.ws.onopen = () => {
            this.addLog('WebSocket connected', 'info');
        };

        this.ws.onmessage = (event) => {
            const message = JSON.parse(event.data);
            this.handleWebSocketMessage(message);
        };

        this.ws.onclose = () => {
            this.addLog('WebSocket disconnected. Attempting to reconnect...', 'warning');
            setTimeout(() => this.connectWebSocket(), 3000);
        };

        this.ws.onerror = (error) => {
            this.addLog('WebSocket error occurred', 'error');
        };
    }

    handleWebSocketMessage(message) {
        switch (message.type) {
            case 'progress':
                this.updateMetrics(message.data);
                break;
            case 'completed':
                this.handleProcessCompleted(message.data);
                break;
            case 'error':
                this.handleProcessError(message.data);
                break;
            case 'log':
                this.addLog(message.data.message, message.data.level || 'info');
                break;
            case 'upload_started':
                this.handleUploadStarted(message.data);
                break;
            case 'hls_ready':
                this.handleHLSReady(message.data);
                break;
        }
    }

    handleUploadStarted(data) {
        // Show live stream container when upload process begins
        this.showLiveStreamContainer();
        this.addLog('Upload process started - live stream viewer now visible', 'info');
    }

    handleHLSReady(data) {
        // This will be called when HLS conversion and upload is complete
        this.addLog('HLS streaming ready', 'success');
    }

    async handleStartCompression(e) {
        e.preventDefault();

        const rtspUrl = this.rtspUrlInput.value.trim();
        const durationMinutes = parseFloat(this.durationInput.value);

        if (!rtspUrl || !durationMinutes) {
            this.addLog('Please enter both RTSP URL and duration', 'error');
            return;
        }

        try {
            this.setUIState('starting');
            
            const response = await fetch('/api/start-compression', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    rtspUrl,
                    durationMinutes
                })
            });

            const result = await response.json();

            if (result.success) {
                this.currentSessionId = result.sessionId;
                this.startTime = new Date();
                this.estimatedDuration = durationMinutes * 60 * 1000; // Convert to milliseconds
                
                this.setUIState('running');
                this.updateStatus('Running', result.sessionId, result.duration);
                this.addLog(`Compression started - Session: ${result.sessionId}`, 'info');
                this.addLog(`Duration: ${result.duration}`, 'info');
                
                this.startProgressTracking();
            } else {
                throw new Error(result.error || 'Failed to start compression');
            }
        } catch (error) {
            this.addLog(`Error: ${error.message}`, 'error');
            this.setUIState('ready');
        }
    }

    async handleStopCompression() {
        if (!this.currentSessionId) return;

        try {
            const response = await fetch(`/api/stop-compression/${this.currentSessionId}`, {
                method: 'POST'
            });

            const result = await response.json();
            
            if (result.success) {
                this.addLog('Process stopped by user', 'warning');
                this.setUIState('ready');
                this.stopProgressTracking();
            }
        } catch (error) {
            this.addLog(`Error stopping process: ${error.message}`, 'error');
        }
    }

    updateMetrics(data) {
        this.rawSize.textContent = data.originalSizeFormatted;
        this.compressedSize.textContent = data.encodedSizeFormatted;
        this.compressionRatio.textContent = `${data.compressionRatio}%`;
        this.bandwidthSavings.textContent = `${data.compressionRatio}%`;

        // Update stream availability
        this.updateStreamAvailability(data);
    }

    updateStreamAvailability(data) {
        // Check if we have compressed data
        if (data.encodedSize > 0 && this.compressedStreamOverlay.innerHTML.includes('Waiting for')) {
            this.showCompressedStreamReady();
        }

        // Check if we have original data
        if (data.originalSize > 0 && this.liveStreamOverlay.innerHTML.includes('Connecting to')) {
            this.showLiveStreamReady();
        }
    }

    handleProcessCompleted(data) {
        this.addLog(`Process completed with exit code: ${data.exitCode}`, data.success ? 'info' : 'error');
        this.setUIState('completed');
        this.stopProgressTracking();

        if (data.success) {
            // Handle S3 transition for compressed stream
            this.handleS3Transition();
            this.generateFinalReport();
        }
    }

    handleProcessError(data) {
        this.addLog(`Process error: ${data.error}`, 'error');
        this.setUIState('ready');
        this.stopProgressTracking();
    }

    setUIState(state) {
        switch (state) {
            case 'ready':
                this.startBtn.style.display = 'inline-flex';
                this.stopBtn.style.display = 'none';
                this.startBtn.disabled = false;
                this.progressSection.style.display = 'none';
                this.streamSection.style.display = 'none';
                this.currentSessionId = null;
                this.updateStatus('Ready', '-', '-');
                this.hideStreams();
                break;
            case 'starting':
                this.startBtn.disabled = true;
                this.updateStatus('Starting...', '-', '-');
                break;
            case 'running':
                this.startBtn.style.display = 'none';
                this.stopBtn.style.display = 'inline-flex';
                this.progressSection.style.display = 'block';
                this.streamSection.style.display = 'block';
                this.updateStatus('Running', this.currentSessionId, this.configuredDuration.textContent);
                this.initializeStreams();
                break;
            case 'completed':
                this.startBtn.style.display = 'inline-flex';
                this.stopBtn.style.display = 'none';
                this.startBtn.disabled = false;
                this.updateStatus('Completed', this.currentSessionId, this.configuredDuration.textContent);
                break;
        }
    }

    updateStatus(status, sessionId, duration) {
        this.currentStatus.textContent = status;
        this.sessionIdDisplay.textContent = sessionId;
        this.configuredDuration.textContent = duration;
    }

    startProgressTracking() {
        this.progressInterval = setInterval(() => {
            if (!this.startTime || !this.estimatedDuration) return;

            const elapsed = Date.now() - this.startTime.getTime();
            const progress = Math.min((elapsed / this.estimatedDuration) * 100, 100);
            const remaining = Math.max(this.estimatedDuration - elapsed, 0);

            this.progressFill.style.width = `${progress}%`;
            this.progressPercent.textContent = `${Math.round(progress)}%`;
            
            if (remaining > 0) {
                const minutes = Math.floor(remaining / 60000);
                const seconds = Math.floor((remaining % 60000) / 1000);
                this.timeRemaining.textContent = `${minutes}:${seconds.toString().padStart(2, '0')} remaining`;
            } else {
                this.timeRemaining.textContent = 'Finalizing...';
            }
        }, 1000);
    }

    stopProgressTracking() {
        if (this.progressInterval) {
            clearInterval(this.progressInterval);
            this.progressInterval = null;
        }
    }

    addLog(message, level = 'info') {
        const logEntry = document.createElement('div');
        logEntry.className = `log-entry ${level}`;
        
        const timestamp = new Date().toLocaleTimeString();
        logEntry.innerHTML = `
            <span class="log-time">[${timestamp}]</span>
            <span class="log-message">${message}</span>
        `;
        
        this.logsContainer.appendChild(logEntry);
        this.logsContainer.scrollTop = this.logsContainer.scrollHeight;
    }

    initializeStreams() {
        // Show live stream section but hide video containers initially
        this.streamSection.style.display = 'block';
        this.streamSection.classList.add('fade-in');

        // Hide video containers until HLS streams are ready
        this.hideVideoContainers();

        // Set up live RTSP stream display (but keep hidden)
        this.setupLiveStream();

        // Monitor for compressed stream availability
        this.monitorCompressedStream();
    }

    hideVideoContainers() {
        // Hide both video containers initially
        const liveStreamBox = document.querySelector('.stream-box:first-child');
        const compressedStreamBox = document.querySelector('.stream-box:last-child');

        if (liveStreamBox) {
            liveStreamBox.style.display = 'none';
        }
        if (compressedStreamBox) {
            compressedStreamBox.style.display = 'none';
        }

        this.addLog('Stream viewers hidden until processing begins', 'info');
    }

    showLiveStreamContainer() {
        // Show only the live stream container when upload begins
        const liveStreamBox = document.querySelector('.stream-box:first-child');

        if (liveStreamBox) {
            liveStreamBox.style.display = 'block';
            liveStreamBox.classList.add('fade-in');
        }

        this.addLog('Live stream viewer now visible - upload in progress', 'info');
    }

    showCompressedStreamContainer() {
        // Show the compressed stream container when HLS is ready
        const compressedStreamBox = document.querySelector('.stream-box:last-child');

        if (compressedStreamBox) {
            compressedStreamBox.style.display = 'block';
            compressedStreamBox.classList.add('fade-in');
        }

        this.addLog('Compressed stream viewer now visible - HLS ready', 'success');
    }

    showVideoContainers() {
        // Show both video containers (fallback method)
        this.showLiveStreamContainer();
        this.showCompressedStreamContainer();
    }

    setupLiveStream() {
        // Get the RTSP URL from the form
        const rtspUrl = this.rtspUrlInput.value.trim();

        // Show live stream info and try to display it
        this.liveStreamOverlay.innerHTML = `
            <i class="fas fa-play-circle"></i>
            <p>Live RTSP Stream</p>
            <small style="color: #639884; margin-top: 5px;">${rtspUrl}</small>
        `;

        // Try to set up HLS stream for the live feed
        this.setupHLSStream(rtspUrl);
    }

    async setupHLSStream(rtspUrl) {
        try {
            // Request HLS conversion from backend
            const response = await fetch('/api/hls/start', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ rtspUrl, sessionId: this.currentSessionId })
            });

            if (response.ok) {
                const result = await response.json();
                if (result.hlsUrl) {
                    this.loadHLSStream(result.hlsUrl);
                }
            } else {
                this.showLiveStreamFallback();
            }
        } catch (error) {
            console.log('HLS conversion not available, showing status only');
            this.showLiveStreamFallback();
        }
    }

    loadHLSStream(hlsUrl) {
        if (Hls.isSupported()) {
            // Use HLS.js for browsers that don't support HLS natively
            this.hls = new Hls({
                enableWorker: true,
                lowLatencyMode: true,
                backBufferLength: 90
            });

            this.hls.loadSource(hlsUrl);
            this.hls.attachMedia(this.liveStream);

            this.hls.on(Hls.Events.MANIFEST_PARSED, () => {
                this.liveStreamOverlay.innerHTML = `
                    <i class="fas fa-play-circle"></i>
                    <p>Live Stream Ready</p>
                    <small style="color: #639884; margin-top: 5px;">HLS stream loaded</small>
                `;
                setTimeout(() => {
                    this.liveStreamOverlay.classList.add('hidden');
                    this.liveStream.play().catch(e => console.log('Autoplay prevented'));
                }, 1000);
                this.addLog('Live HLS stream loaded', 'info');
            });

            this.hls.on(Hls.Events.ERROR, (event, data) => {
                console.error('HLS error:', data);
                this.showLiveStreamFallback();
            });
        } else if (this.liveStream.canPlayType('application/vnd.apple.mpegurl')) {
            // Native HLS support (Safari)
            this.liveStream.src = hlsUrl;
            this.liveStream.addEventListener('loadedmetadata', () => {
                this.liveStreamOverlay.classList.add('hidden');
                this.liveStream.play().catch(e => console.log('Autoplay prevented'));
                this.addLog('Live HLS stream loaded (native)', 'info');
            });
        } else {
            this.showLiveStreamFallback();
        }
    }



    showLiveStreamFallback() {
        this.liveStreamOverlay.innerHTML = `
            <i class="fas fa-video"></i>
            <p>RTSP Stream Active</p>
            <small style="color: #639884; margin-top: 5px;">Live capture in progress</small>
        `;
    }

    showLiveStreamReady() {
        this.liveStreamOverlay.innerHTML = `
            <i class="fas fa-play-circle"></i>
            <p>Raw stream being captured</p>
            <small style="color: #639884; margin-top: 5px;">Live data available</small>
        `;
    }

    monitorCompressedStream() {
        // Start checking for compressed file availability
        this.compressedStreamCheckInterval = setInterval(() => {
            if (this.currentSessionId) {
                this.checkCompressedStreamAvailability();
            }
        }, 2000);

        // Also listen for process completion to handle S3 transition
        this.setupS3TransitionHandling();
    }

    async checkCompressedStreamAvailability() {
        if (!this.currentSessionId) return;

        try {
            // Check for HLS streams first (preferred for web streaming)
            const hlsResponse = await fetch(`/api/hls-streams/${this.currentSessionId}`);
            if (hlsResponse.ok) {
                const hlsStreams = await hlsResponse.json();
                if (hlsStreams.length > 0 && !this.compressedStreamLoaded) {
                    const latestHLS = hlsStreams[hlsStreams.length - 1];
                    this.loadCompressedHLSStream(latestHLS.url, latestHLS.filename);
                    return;
                }
            }

            // Fallback to regular streams API for local files
            const response = await fetch(`/api/streams/${this.currentSessionId}`);
            if (response.ok) {
                const streams = await response.json();

                // Check if we have any encoded files locally
                if (streams.encoded && streams.encoded.length > 0) {
                    const encodedFile = streams.encoded[0]; // Get the first/latest encoded file
                    if (!this.compressedStreamLoaded) {
                        this.loadCompressedStream(encodedFile.url, encodedFile.filename, 'local');
                    }
                    return;
                }
            }
        } catch (error) {
            console.log('Error checking stream availability:', error);
        }
    }

    setupS3TransitionHandling() {
        // This will be called when the process completes and files move to S3
        // We'll handle this in the WebSocket message handler
    }

    showCompressedStreamReady() {
        this.compressedStreamOverlay.innerHTML = `
            <i class="fas fa-cog fa-spin"></i>
            <p>Compression in progress</p>
            <small style="color: #639884; margin-top: 5px;">Generating compressed stream...</small>
        `;

        // Start monitoring for the actual file
        if (!this.compressedStreamCheckInterval) {
            this.monitorCompressedStream();
        }
    }

    loadCompressedHLSStream(hlsUrl, filename) {
        this.compressedStreamLoaded = true;

        // Show only the compressed stream container now that HLS is ready
        this.showCompressedStreamContainer();

        this.compressedStreamOverlay.innerHTML = `
            <i class="fas fa-cloud-download-alt"></i>
            <p>Loading HLS Stream</p>
            <small style="color: #639884; margin-top: 5px;">S3 HLS: ${filename}</small>
        `;

        // Validate HLS URL format
        if (!hlsUrl.includes('.m3u8')) {
            console.error('Invalid HLS URL - missing .m3u8:', hlsUrl);
            this.compressedStreamOverlay.innerHTML = `
                <i class="fas fa-exclamation-triangle"></i>
                <p>Invalid HLS URL</p>
                <small style="color: #fc8181; margin-top: 5px;">URL must point to .m3u8 playlist</small>
            `;
            return;
        }

        this.addLog(`Loading HLS playlist: ${hlsUrl}`, 'info');

        // Load HLS stream using HLS.js
        if (Hls.isSupported()) {
            // Clean up existing HLS instance for compressed stream
            if (this.compressedHls) {
                this.compressedHls.destroy();
            }

            this.compressedHls = new Hls({
                enableWorker: true,
                lowLatencyMode: false, // Better for recorded content
                backBufferLength: 90,
                debug: false
            });

            this.compressedHls.loadSource(hlsUrl);
            this.compressedHls.attachMedia(this.compressedStream);

            this.compressedHls.on(Hls.Events.MANIFEST_PARSED, () => {
                this.compressedStreamOverlay.innerHTML = `
                    <i class="fas fa-play-circle"></i>
                    <p>HLS Stream Ready</p>
                    <small style="color: #639884; margin-top: 5px;">Playlist loaded successfully</small>
                `;

                setTimeout(() => {
                    this.compressedStreamOverlay.classList.add('hidden');
                    this.compressedStream.play().catch(e => console.log('Autoplay prevented'));
                }, 1500);

                this.addLog(`✅ HLS playlist loaded successfully: ${filename}`, 'success');

                // Stop monitoring since we have the HLS stream
                if (this.compressedStreamCheckInterval) {
                    clearInterval(this.compressedStreamCheckInterval);
                    this.compressedStreamCheckInterval = null;
                }
            });

            this.compressedHls.on(Hls.Events.ERROR, (event, data) => {
                console.error('Compressed HLS error:', data);
                this.compressedStreamOverlay.innerHTML = `
                    <i class="fas fa-exclamation-triangle"></i>
                    <p>HLS Stream Error</p>
                    <small style="color: #fc8181; margin-top: 5px;">Failed to load playlist: ${data.type}</small>
                `;
                this.addLog(`❌ HLS error: ${data.type} - ${data.details}`, 'error');
            });

        } else if (this.compressedStream.canPlayType('application/vnd.apple.mpegurl')) {
            // Native HLS support (Safari)
            this.compressedStream.src = hlsUrl;
            this.compressedStream.addEventListener('loadedmetadata', () => {
                this.compressedStreamOverlay.classList.add('hidden');
                this.addLog(`✅ HLS stream loaded (native): ${filename}`, 'success');

                // Stop monitoring
                if (this.compressedStreamCheckInterval) {
                    clearInterval(this.compressedStreamCheckInterval);
                    this.compressedStreamCheckInterval = null;
                }
            });
        } else {
            // Fallback to regular video loading
            this.addLog('HLS not supported, falling back to direct video', 'warning');
            this.loadCompressedStream(hlsUrl, filename, 's3');
        }
    }

    loadCompressedStream(streamUrl, filename, source = 'local') {
        this.compressedStreamLoaded = true;
        this.compressedStream.src = streamUrl;

        const sourceLabel = source === 'local' ? 'Local file' : 'S3 file';
        this.compressedStreamOverlay.innerHTML = `
            <i class="fas fa-play-circle"></i>
            <p>Compressed Stream Ready</p>
            <small style="color: #639884; margin-top: 5px;">${sourceLabel}: ${filename}</small>
        `;

        // Set up video event handlers
        this.compressedStream.addEventListener('loadedmetadata', () => {
            this.compressedStreamOverlay.classList.add('hidden');
            this.addLog(`Compressed stream loaded from ${source}: ${filename}`, 'info');
        });

        this.compressedStream.addEventListener('error', (e) => {
            console.error('Video loading error:', e);
            this.compressedStreamOverlay.innerHTML = `
                <i class="fas fa-exclamation-triangle"></i>
                <p>Stream Error</p>
                <small style="color: #fc8181; margin-top: 5px;">Failed to load video</small>
            `;
        });

        // If this is a local file and we're still processing, keep monitoring for S3 transition
        if (source === 'local') {
            // Don't clear the interval yet - we want to detect S3 transition
            this.addLog(`Playing local compressed file: ${filename}`, 'info');
        } else {
            // This is from S3, we can stop monitoring
            if (this.compressedStreamCheckInterval) {
                clearInterval(this.compressedStreamCheckInterval);
                this.compressedStreamCheckInterval = null;
            }
            this.addLog(`Switched to S3 file: ${filename}`, 'info');
        }
    }

    handleS3Transition() {
        // Called when files are moved to S3
        if (this.compressedStreamLoaded) {
            this.compressedStreamOverlay.innerHTML = `
                <i class="fas fa-cloud-upload-alt"></i>
                <p>Files Uploaded to S3</p>
                <small style="color: #639884; margin-top: 5px;">Compression complete</small>
            `;

            // Clear monitoring since files are now in S3
            if (this.compressedStreamCheckInterval) {
                clearInterval(this.compressedStreamCheckInterval);
                this.compressedStreamCheckInterval = null;
            }
        }
    }

    hideStreams() {
        // Clean up HLS instances
        if (this.hls) {
            this.hls.destroy();
            this.hls = null;
        }

        if (this.compressedHls) {
            this.compressedHls.destroy();
            this.compressedHls = null;
        }

        // Hide video containers
        this.hideVideoContainers();

        // Reset stream states
        this.compressedStreamLoaded = false;
        this.liveStream.src = '';
        this.compressedStream.src = '';

        // Clear monitoring intervals
        if (this.compressedStreamCheckInterval) {
            clearInterval(this.compressedStreamCheckInterval);
            this.compressedStreamCheckInterval = null;
        }

        // Reset overlays
        this.liveStreamOverlay.classList.remove('hidden');
        this.compressedStreamOverlay.classList.remove('hidden');

        this.liveStreamOverlay.innerHTML = `
            <i class="fas fa-spinner fa-spin"></i>
            <p>Connecting to live stream...</p>
        `;

        this.compressedStreamOverlay.innerHTML = `
            <i class="fas fa-clock"></i>
            <p>Waiting for HLS upload to complete...</p>
        `;
    }

    generateFinalReport() {
        const rawSizeText = this.rawSize.textContent;
        const compressedSizeText = this.compressedSize.textContent;
        const compressionRatioText = this.compressionRatio.textContent;
        const bandwidthSavingsText = this.bandwidthSavings.textContent;

        // Calculate space saved in bytes if we have the data
        let spaceSavedText = compressionRatioText;

        const reportHTML = `
            <div class="report-grid">
                <div class="report-item">
                    <h4>Original Size</h4>
                    <div class="value">${rawSizeText}</div>
                </div>
                <div class="report-item">
                    <h4>Compressed Size</h4>
                    <div class="value">${compressedSizeText}</div>
                </div>
                <div class="report-item">
                    <h4>Space Saved</h4>
                    <div class="value">${spaceSavedText}</div>
                </div>
                <div class="report-item">
                    <h4>Bandwidth Savings</h4>
                    <div class="value">${bandwidthSavingsText}</div>
                </div>
            </div>
            <div style="margin-top: 20px; padding: 15px; background: #2d2d2d; border-radius: 8px; border: 1px solid rgba(252, 149, 70, 0.2);">
                <h4 style="margin-bottom: 10px; color: #FC9546;">Summary</h4>
                <p style="color: #e2e8f0; line-height: 1.5;">Compression completed successfully. Files have been uploaded to S3 and local copies have been cleaned up.</p>
                <p style="margin-top: 10px; color: #FC9546;"><strong>Compression Results:</strong></p>
                <ul style="margin-left: 20px; margin-top: 5px; color: #639884; line-height: 1.6;">
                    <li>Original file size: <strong style="color: #e2e8f0;">${rawSizeText}</strong></li>
                    <li>Compressed file size: <strong style="color: #e2e8f0;">${compressedSizeText}</strong></li>
                    <li>Space saved: <strong style="color: #FC9546;">${spaceSavedText}</strong></li>
                    <li>Bandwidth reduction: <strong style="color: #FC9546;">${bandwidthSavingsText}</strong></li>
                </ul>
                <p style="margin-top: 15px; color: #FC9546;"><strong>S3 Buckets:</strong></p>
                <ul style="margin-left: 20px; margin-top: 5px; color: #639884;">
                    <li>Original: <code style="background: #1b1b1b; padding: 2px 6px; border-radius: 4px; color: #FC9546;">original-streams</code></li>
                    <li>Compressed: <code style="background: #1b1b1b; padding: 2px 6px; border-radius: 4px; color: #FC9546;">compressed-streams</code></li>
                </ul>
            </div>
        `;

        this.finalReport.innerHTML = reportHTML;
        this.reportSection.style.display = 'block';
        this.reportSection.classList.add('fade-in');
    }
}

// Initialize the dashboard when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new RTSPDashboard();
});
