#!/bin/bash
# ─────────────────────────────────────────────
# Script: auto_process_to_hls.sh
# Purpose:
#   - Automatically process completed .ts files to HLS
#   - Extract session ID from filename for web dashboard integration
#   - Upload to S3 and make available for web streaming
# ─────────────────────────────────────────────

# 📋 Configuration
S3_BUCKET="${S3_BUCKET:-your-streaming-bucket}"
S3_PREFIX="${S3_PREFIX:-streams/}"
ORIGINAL_DIR="original/stream"
PROCESSED_DIR="processed"

# Create directories
mkdir -p "$PROCESSED_DIR"
mkdir -p logs

echo "🚀 Auto HLS Processing Service"
echo "📂 Monitoring: $ORIGINAL_DIR"
echo "☁️ S3 Bucket: $S3_BUCKET"
echo "📁 S3 Prefix: $S3_PREFIX"
echo ""

# 🔧 Function to extract session ID from filename
extract_session_id() {
    local filename="$1"
    # Extract timestamp from filename like "stream_20241217_143022.ts"
    # Use timestamp as session ID for web dashboard
    if [[ "$filename" =~ stream_([0-9]{8}_[0-9]{6})\.ts ]]; then
        echo "${BASH_REMATCH[1]}"
    else
        echo ""
    fi
}

# 🔧 Function to process a single .ts file
process_ts_file() {
    local ts_file="$1"
    local basename=$(basename "$ts_file")
    local processed_marker="${PROCESSED_DIR}/${basename}.hls_processed"
    
    # Skip if already processed
    if [ -f "$processed_marker" ]; then
        echo "⏭️ Already processed: $basename"
        return 0
    fi
    
    # Extract session ID for web dashboard integration
    local session_id=$(extract_session_id "$basename")
    
    echo "🔄 Processing: $ts_file"
    if [ -n "$session_id" ]; then
        echo "🆔 Session ID: $session_id"
    fi
    
    # Convert to HLS and upload with session ID
    if ./convert_to_hls_and_upload.sh "$ts_file" "$S3_BUCKET" "$S3_PREFIX" "$session_id"; then
        # Mark as processed
        touch "$processed_marker"
        echo "✅ Completed: $basename"
        
        # Log the stream URL
        local hls_basename=$(basename "$ts_file" .ts)
        local url_file="hls/${hls_basename}/stream_url.txt"
        if [ -f "$url_file" ]; then
            local stream_url=$(cat "$url_file")
            echo "🌐 Stream URL: $stream_url"
            
            # If we have a session ID, log web dashboard availability
            if [ -n "$session_id" ]; then
                echo "📱 Available in web dashboard for session: $session_id"
            fi
        fi
        
        echo ""
    else
        echo "❌ Failed to process: $basename"
        return 1
    fi
}

# 🎯 Check for required tools
if ! command -v ffmpeg &> /dev/null; then
    echo "❌ ffmpeg not found. Please install ffmpeg."
    exit 1
fi

if ! command -v aws &> /dev/null; then
    echo "❌ AWS CLI not found. Please install and configure AWS CLI."
    exit 1
fi

# 📥 Parse arguments
MODE="${1:-monitor}"

if [ "$MODE" = "help" ] || [ "$MODE" = "--help" ]; then
    echo "📖 Usage: $0 [MODE]"
    echo ""
    echo "Modes:"
    echo "  monitor  - Continuously monitor for new .ts files (default)"
    echo "  manual   - Process all existing .ts files once"
    echo "  help     - Show this help"
    echo ""
    echo "Environment variables:"
    echo "  S3_BUCKET - S3 bucket name (default: your-streaming-bucket)"
    echo "  S3_PREFIX - S3 path prefix (default: streams/)"
    echo ""
    echo "Examples:"
    echo "  S3_BUCKET=my-bucket ./auto_process_to_hls.sh monitor"
    echo "  S3_BUCKET=my-bucket S3_PREFIX=live/ ./auto_process_to_hls.sh manual"
    exit 0
fi

# 🎯 Manual mode - process all existing files
if [ "$MODE" = "manual" ]; then
    echo "🔍 Processing all .ts files in $ORIGINAL_DIR..."
    
    if [ ! -d "$ORIGINAL_DIR" ]; then
        echo "❌ Directory not found: $ORIGINAL_DIR"
        exit 1
    fi
    
    # Find and process all .ts files
    find "$ORIGINAL_DIR" -name "*.ts" -type f | while read -r ts_file; do
        process_ts_file "$ts_file"
    done
    
    echo "🎉 Manual processing completed!"

# 👀 Monitor mode - watch for new files
elif [ "$MODE" = "monitor" ]; then
    echo "👀 Monitoring $ORIGINAL_DIR for new .ts files..."
    echo "Press Ctrl+C to stop monitoring"
    echo ""
    
    if ! command -v inotifywait &> /dev/null; then
        echo "❌ inotifywait not found. Install inotify-tools:"
        echo "   Ubuntu/Debian: sudo apt-get install inotify-tools"
        echo "   CentOS/RHEL: sudo yum install inotify-tools"
        exit 1
    fi
    
    # Process any existing files first
    if [ -d "$ORIGINAL_DIR" ]; then
        find "$ORIGINAL_DIR" -name "*.ts" -type f | while read -r ts_file; do
            process_ts_file "$ts_file"
        done
    fi
    
    # Monitor for file creation and modification
    inotifywait -m -r -e close_write,moved_to "$ORIGINAL_DIR" --format '%w%f' 2>/dev/null | while read -r file; do
        if [[ "$file" == *.ts ]]; then
            echo "📁 New file detected: $file"
            # Small delay to ensure file is completely written
            sleep 2
            process_ts_file "$file"
        fi
    done

else
    echo "❌ Unknown mode: $MODE"
    echo "Use 'manual', 'monitor', or 'help'"
    exit 1
fi
