#!/bin/bash
# ─────────────────────────────────────────────
# Script: test_hls_streaming.sh
# Purpose:
#   - Test HLS streaming functionality end-to-end
#   - Validate playlist generation and S3 upload
#   - Test web dashboard integration
# ─────────────────────────────────────────────

echo "🧪 HLS Streaming Test Suite"
echo "=========================="
echo ""

# 📋 Configuration
TEST_RTSP_URL="rtsp://wowzaec2demo.streamlock.net/vod/mp4:BigBuckBunny_115k.mp4"
TEST_DURATION="00:00:30"  # 30 seconds for quick test
S3_BUCKET="${S3_BUCKET:-test-streaming-bucket}"
S3_PREFIX="${S3_PREFIX:-test-streams/}"
TEST_SESSION_ID="test_$(date +%Y%m%d_%H%M%S)"

echo "🔧 Test Configuration:"
echo "   RTSP URL: $TEST_RTSP_URL"
echo "   Duration: $TEST_DURATION"
echo "   S3 Bucket: $S3_BUCKET"
echo "   Session ID: $TEST_SESSION_ID"
echo ""

# 🔍 Pre-flight checks
echo "🔍 Pre-flight Checks:"

# Check required tools
MISSING_TOOLS=()

if ! command -v ffmpeg &> /dev/null; then
    MISSING_TOOLS+=("ffmpeg")
fi

if ! command -v aws &> /dev/null; then
    MISSING_TOOLS+=("aws")
fi

if ! command -v curl &> /dev/null; then
    MISSING_TOOLS+=("curl")
fi

if [ ${#MISSING_TOOLS[@]} -gt 0 ]; then
    echo "❌ Missing required tools: ${MISSING_TOOLS[*]}"
    echo "   Please install missing tools and try again."
    exit 1
fi

echo "✅ All required tools found"

# Check AWS credentials
if ! aws sts get-caller-identity &> /dev/null; then
    echo "❌ AWS credentials not configured"
    echo "   Run: aws configure"
    exit 1
fi

echo "✅ AWS credentials configured"

# Check S3 bucket access
if ! aws s3 ls "s3://$S3_BUCKET" &> /dev/null; then
    echo "⚠️  S3 bucket '$S3_BUCKET' not accessible"
    echo "   Creating bucket..."
    if aws s3 mb "s3://$S3_BUCKET" 2>/dev/null; then
        echo "✅ S3 bucket created"
    else
        echo "❌ Failed to create S3 bucket"
        exit 1
    fi
else
    echo "✅ S3 bucket accessible"
fi

echo ""

# 🎬 Test 1: RTSP Capture
echo "🎬 Test 1: RTSP Stream Capture"
echo "------------------------------"

TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
TEST_TS_FILE="original/stream/stream_${TIMESTAMP}.ts"

echo "📡 Capturing RTSP stream..."
echo "   Source: $TEST_RTSP_URL"
echo "   Output: $TEST_TS_FILE"

if ./save_rtsp_raw_stream.sh "$TEST_RTSP_URL" "$TEST_DURATION" "$TIMESTAMP"; then
    echo "✅ RTSP capture completed"
    
    if [ -f "$TEST_TS_FILE" ]; then
        FILE_SIZE=$(stat -f%z "$TEST_TS_FILE" 2>/dev/null || stat -c%s "$TEST_TS_FILE" 2>/dev/null)
        echo "   File size: $FILE_SIZE bytes"
        
        if [ "$FILE_SIZE" -gt 1000 ]; then
            echo "✅ Captured file has valid size"
        else
            echo "❌ Captured file too small (likely empty)"
            exit 1
        fi
    else
        echo "❌ Captured file not found"
        exit 1
    fi
else
    echo "❌ RTSP capture failed"
    exit 1
fi

echo ""

# 🔄 Test 2: HLS Conversion
echo "🔄 Test 2: HLS Conversion and S3 Upload"
echo "---------------------------------------"

echo "🚀 Converting to HLS format..."
if ./convert_to_hls_and_upload.sh "$TEST_TS_FILE" "$S3_BUCKET" "$S3_PREFIX" "$TEST_SESSION_ID"; then
    echo "✅ HLS conversion and upload completed"
    
    # Check local HLS files
    HLS_BASENAME=$(basename "$TEST_TS_FILE" .ts)
    HLS_DIR="hls/${HLS_BASENAME}"
    
    if [ -f "${HLS_DIR}/playlist.m3u8" ]; then
        echo "✅ HLS playlist created locally"
        
        # Count segments
        SEGMENT_COUNT=$(ls "${HLS_DIR}"/segment_*.ts 2>/dev/null | wc -l)
        echo "   Segments created: $SEGMENT_COUNT"
        
        if [ "$SEGMENT_COUNT" -gt 0 ]; then
            echo "✅ HLS segments created"
        else
            echo "❌ No HLS segments found"
            exit 1
        fi
    else
        echo "❌ HLS playlist not found"
        exit 1
    fi
    
    # Check S3 URL file
    if [ -f "${HLS_DIR}/stream_url.txt" ]; then
        S3_URL=$(cat "${HLS_DIR}/stream_url.txt")
        echo "✅ S3 URL generated: $S3_URL"
    else
        echo "❌ S3 URL file not found"
        exit 1
    fi
    
else
    echo "❌ HLS conversion failed"
    exit 1
fi

echo ""

# 🌐 Test 3: S3 Accessibility
echo "🌐 Test 3: S3 Stream Accessibility"
echo "----------------------------------"

echo "🔍 Testing S3 playlist accessibility..."
if curl -s --head "$S3_URL" | grep -q "200 OK"; then
    echo "✅ S3 playlist accessible via HTTP"
    
    # Test playlist content
    PLAYLIST_CONTENT=$(curl -s "$S3_URL")
    if echo "$PLAYLIST_CONTENT" | grep -q "#EXTM3U"; then
        echo "✅ Valid HLS playlist content"
        
        # Count segments in playlist
        PLAYLIST_SEGMENTS=$(echo "$PLAYLIST_CONTENT" | grep -c "\.ts")
        echo "   Segments in playlist: $PLAYLIST_SEGMENTS"
        
        if [ "$PLAYLIST_SEGMENTS" -gt 0 ]; then
            echo "✅ Playlist contains segments"
        else
            echo "❌ Playlist contains no segments"
            exit 1
        fi
    else
        echo "❌ Invalid playlist content"
        exit 1
    fi
else
    echo "❌ S3 playlist not accessible"
    echo "   URL: $S3_URL"
    exit 1
fi

echo ""

# 📱 Test 4: Web Dashboard Integration
echo "📱 Test 4: Web Dashboard Integration"
echo "-----------------------------------"

# Check if web dashboard integration files exist
WEB_HLS_DIR="hls/${TEST_SESSION_ID}/${HLS_BASENAME}"
if [ -f "${WEB_HLS_DIR}/stream_url.txt" ]; then
    echo "✅ Web dashboard integration file created"
    
    WEB_URL=$(cat "${WEB_HLS_DIR}/stream_url.txt")
    echo "   Web URL: $WEB_URL"
    
    if [ "$WEB_URL" = "$S3_URL" ]; then
        echo "✅ Web URL matches S3 URL"
    else
        echo "❌ Web URL mismatch"
        exit 1
    fi
else
    echo "❌ Web dashboard integration file missing"
    exit 1
fi

# Test web API endpoint (if server is running)
echo "🔍 Testing web API endpoint..."
if curl -s "http://localhost:3000/api/hls-streams/$TEST_SESSION_ID" > /dev/null 2>&1; then
    API_RESPONSE=$(curl -s "http://localhost:3000/api/hls-streams/$TEST_SESSION_ID")
    if echo "$API_RESPONSE" | grep -q "$HLS_BASENAME"; then
        echo "✅ Web API returns HLS stream info"
    else
        echo "⚠️  Web API response doesn't contain expected stream"
        echo "   Response: $API_RESPONSE"
    fi
else
    echo "⚠️  Web server not running (http://localhost:3000)"
    echo "   Start server with: npm start"
fi

echo ""

# 🎉 Test Summary
echo "🎉 Test Summary"
echo "==============="
echo "✅ RTSP stream capture: PASSED"
echo "✅ HLS conversion: PASSED"
echo "✅ S3 upload: PASSED"
echo "✅ S3 accessibility: PASSED"
echo "✅ Web integration: PASSED"
echo ""
echo "🌐 Test Stream URL: $S3_URL"
echo "🆔 Test Session ID: $TEST_SESSION_ID"
echo ""
echo "🧹 Cleanup:"
echo "   Local files: $TEST_TS_FILE, $HLS_DIR"
echo "   S3 files: s3://$S3_BUCKET/$S3_PREFIX$HLS_BASENAME/"
echo ""
echo "🎬 You can now test in the web dashboard:"
echo "   1. Open http://localhost:3000"
echo "   2. Look for session: $TEST_SESSION_ID"
echo "   3. The compressed stream should load from S3 HLS"
echo ""
echo "✅ All tests passed! HLS streaming is working correctly."
