#!/bin/bash
# ─────────────────────────────────────────────
# Script: convert_to_mp4_and_upload.sh
# Purpose:
#   - Convert .ts file to .mp4 format
#   - Upload MP4 files to S3 bucket
#   - Use simple names without timestamps to save space
# ─────────────────────────────────────────────

# 📥 Parse input arguments
TS_FILE="$1"
S3_BUCKET="$2"
S3_PREFIX="$3"  # Optional prefix for S3 path
SESSION_ID="$4" # Optional session ID for web dashboard integration

if [ -z "$TS_FILE" ] || [ -z "$S3_BUCKET" ]; then
    echo "❗ Usage: $0 <TS_FILE> <S3_BUCKET> [S3_PREFIX] [SESSION_ID]"
    echo "   Example: $0 original/stream/stream_123.ts my-streaming-bucket videos/ session123"
    exit 1
fi

# ✅ Check if input file exists
if [ ! -f "$TS_FILE" ]; then
    echo "❌ Input file not found: $TS_FILE"
    exit 1
fi

# 📂 Create output directories
MP4_DIR="mp4"
mkdir -p "$MP4_DIR"
mkdir -p logs

# 🕒 Timestamp for logging
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="logs/mp4_conversion_${TIMESTAMP}.log"

echo "🎬 Converting .ts to .mp4 format..." | tee -a "$LOG_FILE"
echo "📁 Input: $TS_FILE" | tee -a "$LOG_FILE"

# 🔄 Determine output file name based on input path
if [[ "$TS_FILE" == *"original"* ]]; then
    OUTPUT_MP4="${MP4_DIR}/original.mp4"
    S3_FILENAME="original.mp4"
    echo "📁 Output: $OUTPUT_MP4 (original stream)" | tee -a "$LOG_FILE"
else
    OUTPUT_MP4="${MP4_DIR}/compressed.mp4"
    S3_FILENAME="compressed.mp4"
    echo "📁 Output: $OUTPUT_MP4 (compressed stream)" | tee -a "$LOG_FILE"
fi

# 🔄 Convert to MP4 using ffmpeg (simple copy, no re-encoding)
ffmpeg -y -hide_banner -loglevel info \
    -i "$TS_FILE" \
    -c copy \
    "$OUTPUT_MP4" 2>> "$LOG_FILE"

# ✅ Check conversion success
if [ $? -ne 0 ]; then
    echo "❌ MP4 conversion failed. Check log: $LOG_FILE" | tee -a "$LOG_FILE"
    exit 1
fi

echo "✅ MP4 conversion completed" | tee -a "$LOG_FILE"

# 📤 Upload to S3
echo "☁️ Uploading MP4 file to S3..." | tee -a "$LOG_FILE"

# Get bucket region for correct upload
BUCKET_REGION=$(aws s3api get-bucket-location --bucket "$S3_BUCKET" --query 'LocationConstraint' --output text 2>> "$LOG_FILE")
if [ "$BUCKET_REGION" = "None" ] || [ -z "$BUCKET_REGION" ]; then
    BUCKET_REGION="us-east-1"  # Default region
fi
echo "🌍 Bucket region: $BUCKET_REGION" | tee -a "$LOG_FILE"

# Notify web dashboard that upload is starting (show live stream container)
if [ -n "$SESSION_ID" ]; then
    node notify_websocket.js upload_started "$SESSION_ID" 2>/dev/null || echo "WebSocket notification failed (server may not be running)"
fi

# Set S3 path - use simple names without timestamps
if [ -n "$S3_PREFIX" ]; then
    S3_FILE_PATH="s3://${S3_BUCKET}/${S3_PREFIX}${S3_FILENAME}"
    PUBLIC_URL="https://${S3_BUCKET}.s3.amazonaws.com/${S3_PREFIX}${S3_FILENAME}"
else
    S3_FILE_PATH="s3://${S3_BUCKET}/${S3_FILENAME}"
    PUBLIC_URL="https://${S3_BUCKET}.s3.amazonaws.com/${S3_FILENAME}"
fi

echo "📤 S3 destination: $S3_FILE_PATH" | tee -a "$LOG_FILE"

# Upload MP4 file with proper content type
aws s3 cp "$OUTPUT_MP4" "$S3_FILE_PATH" \
    --content-type "video/mp4" \
    --cache-control "max-age=3600" \
    --region "$BUCKET_REGION" 2>> "$LOG_FILE"

# ✅ Check upload success
if [ $? -eq 0 ]; then
    echo "✅ Upload completed successfully" | tee -a "$LOG_FILE"
    
    echo "" | tee -a "$LOG_FILE"
    echo "🎯 MP4 Video URL:" | tee -a "$LOG_FILE"
    echo "$PUBLIC_URL" | tee -a "$LOG_FILE"
    echo "" | tee -a "$LOG_FILE"
    echo "📋 Use this URL in your web video players" | tee -a "$LOG_FILE"
    
    # Save URL to a file for easy access
    echo "$PUBLIC_URL" > "${MP4_DIR}/${S3_FILENAME%.mp4}_url.txt"
    
    # If SESSION_ID is provided, also save for web dashboard integration
    if [ -n "$SESSION_ID" ]; then
        WEB_MP4_DIR="mp4/${SESSION_ID}"
        mkdir -p "$WEB_MP4_DIR"
        echo "$PUBLIC_URL" > "${WEB_MP4_DIR}/${S3_FILENAME%.mp4}_url.txt"
        echo "📱 Web dashboard integration: ${WEB_MP4_DIR}/${S3_FILENAME%.mp4}_url.txt" | tee -a "$LOG_FILE"
        
        # Notify web dashboard that MP4 is ready
        node notify_websocket.js mp4_ready "$SESSION_ID" "{\"url\":\"$PUBLIC_URL\",\"filename\":\"$S3_FILENAME\",\"type\":\"${S3_FILENAME%.mp4}\"}" 2>/dev/null || echo "WebSocket notification failed"
    fi
    
else
    echo "❌ S3 upload failed. Check AWS credentials and permissions." | tee -a "$LOG_FILE"
    exit 1
fi

echo "🎉 Process completed! MP4 video ready for web viewing." | tee -a "$LOG_FILE"
