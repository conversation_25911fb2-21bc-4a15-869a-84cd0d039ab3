const express = require('express');
const cors = require('cors');
const { spawn, exec } = require('child_process');
const fs = require('fs');
const path = require('path');
const WebSocket = require('ws');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// WebSocket server for real-time updates
const wss = new WebSocket.Server({ port: 8080 });

// Store active processes and their metrics
let activeProcesses = new Map();
let processMetrics = new Map();
let hlsProcesses = new Map();

// Utility function to convert minutes to HH:MM:SS
function minutesToDuration(minutes) {
    const totalSeconds = Math.floor(minutes * 60);
    const hours = Math.floor(totalSeconds / 3600);
    const mins = Math.floor((totalSeconds % 3600) / 60);
    const secs = totalSeconds % 60;
    
    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
}

// Utility function to get total size of .ts files in a folder
function getFolderSize(folderPath) {
    return new Promise((resolve) => {
        exec(`find "${folderPath}" -name "*.ts" -type f -exec stat -c %s {} + 2>/dev/null | awk '{sum+=$1} END {print sum+0}'`, (error, stdout) => {
            if (error) {
                resolve(0);
            } else {
                const size = parseInt(stdout.trim()) || 0;
                resolve(size);
            }
        });
    });
}

// Utility function to format bytes
function formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Sanitize RTSP URL to prevent command injection
function sanitizeRtspUrl(url) {
    // Basic validation - should start with rtsp:// and contain valid characters
    const rtspRegex = /^rtsp:\/\/[a-zA-Z0-9\-._~:/?#[\]@!$&'()*+,;=%]+$/;
    if (!rtspRegex.test(url)) {
        throw new Error('Invalid RTSP URL format');
    }
    return url;
}

// Parse output for file size metrics
function parseOutputForMetrics(output, sessionId) {
    if (!processMetrics.has(sessionId)) {
        processMetrics.set(sessionId, { originalSize: 0, encodedSize: 0 });
    }

    const metrics = processMetrics.get(sessionId);

    // Look for compression completion with file size
    const compressionMatch = output.match(/✅ Compression complete: .+ \((\d+) bytes\)/);
    if (compressionMatch) {
        metrics.encodedSize = parseInt(compressionMatch[1]);
        console.log(`Parsed encoded size: ${metrics.encodedSize} bytes`);
    }

    // Look for file size information in upload output
    const uploadMatch = output.match(/upload: (.+) to s3:\/\/(.+)/);
    if (uploadMatch) {
        const filePath = uploadMatch[1];

        // Get file size before it's deleted
        if (fs.existsSync(filePath)) {
            const stats = fs.statSync(filePath);
            const fileSize = stats.size;

            if (filePath.includes('original/stream/')) {
                metrics.originalSize = fileSize;
                console.log(`Parsed original size: ${metrics.originalSize} bytes`);
            } else if (filePath.includes('encoded/stream/')) {
                // Double-check encoded size from file if not already set
                if (metrics.encodedSize === 0) {
                    metrics.encodedSize = fileSize;
                    console.log(`Parsed encoded size from file: ${metrics.encodedSize} bytes`);
                }
            }
        }
    }

    // Broadcast updated metrics if we have any data
    if (metrics.originalSize > 0 || metrics.encodedSize > 0) {
        const compressionRatio = metrics.originalSize > 0 ?
            ((metrics.originalSize - metrics.encodedSize) / metrics.originalSize * 100) : 0;

        broadcast({
            type: 'progress',
            sessionId,
            data: {
                originalSize: metrics.originalSize,
                encodedSize: metrics.encodedSize,
                originalSizeFormatted: formatBytes(metrics.originalSize),
                encodedSizeFormatted: formatBytes(metrics.encodedSize),
                compressionRatio: compressionRatio.toFixed(2),
                timestamp: new Date().toISOString()
            }
        });
    }
}

// Broadcast message to all connected WebSocket clients
function broadcast(message) {
    wss.clients.forEach(client => {
        if (client.readyState === WebSocket.OPEN) {
            client.send(JSON.stringify(message));
        }
    });
}

// Monitor folder sizes and broadcast updates
async function monitorProgress(sessionId) {
    const originalPath = path.join(__dirname, 'original', 'stream');
    const encodedPath = path.join(__dirname, 'encoded', 'stream');

    console.log(`Starting monitoring for session ${sessionId}`);
    console.log(`Original path: ${originalPath}`);
    console.log(`Encoded path: ${encodedPath}`);

    const interval = setInterval(async () => {
        try {
            const originalSize = await getFolderSize(originalPath);
            const encodedSize = await getFolderSize(encodedPath);

            console.log(`Session ${sessionId} - Original: ${originalSize} bytes, Encoded: ${encodedSize} bytes`);

            const compressionRatio = originalSize > 0 ? ((originalSize - encodedSize) / originalSize * 100) : 0;

            const update = {
                type: 'progress',
                sessionId,
                data: {
                    originalSize,
                    encodedSize,
                    originalSizeFormatted: formatBytes(originalSize),
                    encodedSizeFormatted: formatBytes(encodedSize),
                    compressionRatio: compressionRatio.toFixed(2),
                    timestamp: new Date().toISOString()
                }
            };

            broadcast(update);
        } catch (error) {
            console.error('Error monitoring progress:', error);
        }
    }, 2000); // Update every 2 seconds

    return interval;
}

// API Routes

// Start compression process
app.post('/api/start-compression', async (req, res) => {
    try {
        const { rtspUrl, durationMinutes } = req.body;
        
        // Validate inputs
        if (!rtspUrl || !durationMinutes) {
            return res.status(400).json({ error: 'RTSP URL and duration are required' });
        }
        
        if (durationMinutes <= 0 || durationMinutes > 15) {
            return res.status(400).json({ error: 'Duration must be between 1 and 15 minutes' });
        }
        
        // Sanitize RTSP URL
        const sanitizedUrl = sanitizeRtspUrl(rtspUrl);
        const duration = minutesToDuration(durationMinutes);
        
        // Generate session ID
        const sessionId = Date.now().toString();
        
        // Check if another process is running
        if (activeProcesses.size > 0) {
            return res.status(409).json({ error: 'Another compression process is already running' });
        }
        
        // Start the shell script
        const scriptPath = path.join(__dirname, 'run_both_streams.sh');
        const process = spawn('bash', [scriptPath, sanitizedUrl, duration], {
            cwd: __dirname,
            stdio: ['pipe', 'pipe', 'pipe']
        });
        
        activeProcesses.set(sessionId, process);
        
        // Start monitoring progress
        const monitorInterval = monitorProgress(sessionId);
        
        // Handle process completion
        process.on('close', (code) => {
            clearInterval(monitorInterval);
            activeProcesses.delete(sessionId);

            // Get final file sizes directly from filesystem
            const originalPath = path.join(__dirname, 'original', 'stream');
            const encodedPath = path.join(__dirname, 'encoded', 'stream');

            let finalOriginalSize = 0;
            let finalEncodedSize = 0;

            try {
                // Get actual file sizes for this session
                if (fs.existsSync(originalPath)) {
                    const originalFiles = fs.readdirSync(originalPath)
                        .filter(file => file.includes(sessionId) && file.endsWith('.ts'));

                    for (const file of originalFiles) {
                        const filePath = path.join(originalPath, file);
                        if (fs.existsSync(filePath)) {
                            const stats = fs.statSync(filePath);
                            finalOriginalSize += stats.size;
                        }
                    }
                }

                if (fs.existsSync(encodedPath)) {
                    const encodedFiles = fs.readdirSync(encodedPath)
                        .filter(file => file.includes(sessionId) && file.endsWith('.ts'));

                    for (const file of encodedFiles) {
                        const filePath = path.join(encodedPath, file);
                        if (fs.existsSync(filePath)) {
                            const stats = fs.statSync(filePath);
                            finalEncodedSize += stats.size;
                        }
                    }
                }

                console.log(`Final sizes for session ${sessionId} - Original: ${finalOriginalSize}, Encoded: ${finalEncodedSize}`);

                // Update metrics with final sizes
                if (!processMetrics.has(sessionId)) {
                    processMetrics.set(sessionId, { originalSize: 0, encodedSize: 0 });
                }

                const metrics = processMetrics.get(sessionId);
                if (finalOriginalSize > 0) metrics.originalSize = finalOriginalSize;
                if (finalEncodedSize > 0) metrics.encodedSize = finalEncodedSize;

                const compressionRatio = metrics.originalSize > 0 ?
                    ((metrics.originalSize - metrics.encodedSize) / metrics.originalSize * 100) : 0;

                broadcast({
                    type: 'progress',
                    sessionId,
                    data: {
                        originalSize: metrics.originalSize,
                        encodedSize: metrics.encodedSize,
                        originalSizeFormatted: formatBytes(metrics.originalSize),
                        encodedSizeFormatted: formatBytes(metrics.encodedSize),
                        compressionRatio: compressionRatio.toFixed(2),
                        timestamp: new Date().toISOString()
                    }
                });

            } catch (error) {
                console.error('Error calculating final file sizes:', error);

                // Fallback to existing metrics if file reading fails
                if (processMetrics.has(sessionId)) {
                    const metrics = processMetrics.get(sessionId);
                    const compressionRatio = metrics.originalSize > 0 ?
                        ((metrics.originalSize - metrics.encodedSize) / metrics.originalSize * 100) : 0;

                    broadcast({
                        type: 'progress',
                        sessionId,
                        data: {
                            originalSize: metrics.originalSize,
                            encodedSize: metrics.encodedSize,
                            originalSizeFormatted: formatBytes(metrics.originalSize),
                            encodedSizeFormatted: formatBytes(metrics.encodedSize),
                            compressionRatio: compressionRatio.toFixed(2),
                            timestamp: new Date().toISOString()
                        }
                    });
                }
            }

            broadcast({
                type: 'completed',
                sessionId,
                data: {
                    exitCode: code,
                    success: code === 0,
                    timestamp: new Date().toISOString()
                }
            });

            // Clean up metrics and HLS process
            processMetrics.delete(sessionId);

            // Stop HLS conversion if running
            if (hlsProcesses.has(sessionId)) {
                const hlsProcess = hlsProcesses.get(sessionId);
                hlsProcess.kill('SIGTERM');
                hlsProcesses.delete(sessionId);
            }
        });
        
        // Handle process errors
        process.on('error', (error) => {
            clearInterval(monitorInterval);
            activeProcesses.delete(sessionId);
            processMetrics.delete(sessionId);

            // Stop HLS conversion if running
            if (hlsProcesses.has(sessionId)) {
                const hlsProcess = hlsProcesses.get(sessionId);
                hlsProcess.kill('SIGTERM');
                hlsProcesses.delete(sessionId);
            }

            broadcast({
                type: 'error',
                sessionId,
                data: {
                    error: error.message,
                    timestamp: new Date().toISOString()
                }
            });
        });
        
        // Log process output and extract file size information
        process.stdout.on('data', (data) => {
            const output = data.toString();
            console.log(`Process output: ${output}`);

            // Parse file size information from the output
            parseOutputForMetrics(output, sessionId);

            broadcast({
                type: 'log',
                sessionId,
                data: {
                    message: output,
                    timestamp: new Date().toISOString()
                }
            });
        });
        
        process.stderr.on('data', (data) => {
            console.error(`Process error: ${data}`);
            broadcast({
                type: 'log',
                sessionId,
                data: {
                    message: data.toString(),
                    level: 'error',
                    timestamp: new Date().toISOString()
                }
            });
        });
        
        res.json({
            success: true,
            sessionId,
            message: 'Compression process started',
            duration,
            estimatedCompletionTime: new Date(Date.now() + durationMinutes * 60 * 1000).toISOString()
        });
        
    } catch (error) {
        console.error('Error starting compression:', error);
        res.status(500).json({ error: error.message });
    }
});

// Get current status
app.get('/api/status', (req, res) => {
    res.json({
        activeProcesses: activeProcesses.size,
        sessions: Array.from(activeProcesses.keys())
    });
});

// List available stream files
app.get('/api/streams/:sessionId', (req, res) => {
    const { sessionId } = req.params;

    try {
        const originalDir = path.join(__dirname, 'original', 'stream');
        const encodedDir = path.join(__dirname, 'encoded', 'stream');

        const result = {
            original: [],
            encoded: []
        };

        // Check original files
        if (fs.existsSync(originalDir)) {
            const originalFiles = fs.readdirSync(originalDir)
                .filter(file => file.endsWith('.ts') && file.includes(sessionId))
                .map(file => ({
                    filename: file,
                    size: fs.statSync(path.join(originalDir, file)).size,
                    url: `/api/stream/original/${file}`
                }));
            result.original = originalFiles;
        }

        // Check encoded files
        if (fs.existsSync(encodedDir)) {
            const encodedFiles = fs.readdirSync(encodedDir)
                .filter(file => file.endsWith('.ts') && file.includes(sessionId))
                .map(file => ({
                    filename: file,
                    size: fs.statSync(path.join(encodedDir, file)).size,
                    url: `/api/stream/encoded/${file}`
                }));
            result.encoded = encodedFiles;
        }

        res.json(result);
    } catch (error) {
        console.error('Error listing streams:', error);
        res.status(500).json({ error: 'Failed to list streams' });
    }
});

// Stop compression process
app.post('/api/stop-compression/:sessionId', (req, res) => {
    const { sessionId } = req.params;
    const process = activeProcesses.get(sessionId);

    if (!process) {
        return res.status(404).json({ error: 'Session not found' });
    }

    process.kill('SIGTERM');
    activeProcesses.delete(sessionId);

    res.json({ success: true, message: 'Process stopped' });
});

// Start HLS conversion for live RTSP stream
app.post('/api/hls/start', (req, res) => {
    try {
        const { rtspUrl, sessionId } = req.body;

        if (!rtspUrl || !sessionId) {
            return res.status(400).json({ error: 'RTSP URL and session ID required' });
        }

        // Sanitize RTSP URL
        const sanitizedUrl = sanitizeRtspUrl(rtspUrl);

        // Create HLS output directory
        const hlsDir = path.join(__dirname, 'public', 'hls', sessionId);
        if (!fs.existsSync(hlsDir)) {
            fs.mkdirSync(hlsDir, { recursive: true });
        }

        const playlistPath = path.join(hlsDir, 'playlist.m3u8');
        const segmentPattern = path.join(hlsDir, 'segment_%03d.ts');

        // Start FFmpeg HLS conversion
        const ffmpegArgs = [
            '-i', sanitizedUrl,
            '-c:v', 'libx264',
            '-c:a', 'aac',
            '-preset', 'ultrafast',
            '-tune', 'zerolatency',
            '-f', 'hls',
            '-hls_time', '2',
            '-hls_list_size', '10',
            '-hls_flags', 'delete_segments',
            playlistPath
        ];

        const hlsProcess = spawn('ffmpeg', ffmpegArgs);
        hlsProcesses.set(sessionId, hlsProcess);

        hlsProcess.on('error', (error) => {
            console.error(`HLS conversion error for session ${sessionId}:`, error);
            hlsProcesses.delete(sessionId);
        });

        hlsProcess.on('close', (code) => {
            console.log(`HLS conversion ended for session ${sessionId} with code ${code}`);
            hlsProcesses.delete(sessionId);
        });

        // Return HLS URL
        const hlsUrl = `/hls/${sessionId}/playlist.m3u8`;
        res.json({ success: true, hlsUrl });

    } catch (error) {
        console.error('Error starting HLS conversion:', error);
        res.status(500).json({ error: 'Failed to start HLS conversion' });
    }
});

// Get MP4 streams for a session (from S3)
app.get('/api/mp4-streams/:sessionId', async (req, res) => {
    try {
        const { sessionId } = req.params;

        // Check for MP4 stream URLs in the mp4 directory
        const mp4Dir = path.join(__dirname, 'mp4', sessionId);
        const mp4Streams = [];

        if (fs.existsSync(mp4Dir)) {
            // Look for URL files that contain S3 URLs
            const files = fs.readdirSync(mp4Dir);

            for (const file of files) {
                if (file.endsWith('_url.txt')) {
                    const urlFile = path.join(mp4Dir, file);
                    if (fs.existsSync(urlFile)) {
                        const mp4Url = fs.readFileSync(urlFile, 'utf8').trim();
                        if (mp4Url.startsWith('http')) {
                            const type = file.replace('_url.txt', ''); // original or compressed
                            mp4Streams.push({
                                filename: `${type}.mp4`,
                                url: mp4Url,
                                type: 'mp4',
                                streamType: type,
                                source: 's3'
                            });
                        }
                    }
                }
            }
        }

        res.json(mp4Streams);
    } catch (error) {
        console.error('Error getting MP4 streams:', error);
        res.status(500).json({ error: 'Failed to get MP4 streams' });
    }
});

// Get HLS streams for a session (from S3) - kept for backward compatibility
app.get('/api/hls-streams/:sessionId', async (req, res) => {
    try {
        const { sessionId } = req.params;

        // Check for HLS stream URLs in the hls directory
        const hlsDir = path.join(__dirname, 'hls', sessionId);
        const hlsStreams = [];

        if (fs.existsSync(hlsDir)) {
            // Look for stream_url.txt files that contain S3 URLs
            const files = fs.readdirSync(hlsDir, { withFileTypes: true });

            for (const file of files) {
                if (file.isDirectory()) {
                    const streamUrlFile = path.join(hlsDir, file.name, 'stream_url.txt');
                    if (fs.existsSync(streamUrlFile)) {
                        const hlsUrl = fs.readFileSync(streamUrlFile, 'utf8').trim();
                        if (hlsUrl.startsWith('http')) {
                            hlsStreams.push({
                                filename: file.name,
                                url: hlsUrl,
                                type: 'hls',
                                source: 's3'
                            });
                        }
                    }
                }
            }
        }

        res.json(hlsStreams);
    } catch (error) {
        console.error('Error getting HLS streams:', error);
        res.status(500).json({ error: 'Failed to get HLS streams' });
    }
});

// Serve HLS files
app.use('/hls', express.static(path.join(__dirname, 'public', 'hls')));

// Serve video files for preview (before S3 upload)
app.get('/api/stream/:type/:filename', (req, res) => {
    const { type, filename } = req.params;

    // Validate type
    if (!['original', 'encoded'].includes(type)) {
        return res.status(400).json({ error: 'Invalid stream type' });
    }

    // More flexible filename validation to handle different timestamp formats
    if (!/^stream_.+\.ts$/.test(filename)) {
        return res.status(400).json({ error: 'Invalid filename format' });
    }

    const filePath = path.join(__dirname, type, 'stream', filename);

    // Check if file exists
    if (!fs.existsSync(filePath)) {
        return res.status(404).json({ error: 'File not found' });
    }

    // Set appropriate headers for video streaming
    res.setHeader('Content-Type', 'video/mp2t');
    res.setHeader('Accept-Ranges', 'bytes');

    // Stream the file
    const stat = fs.statSync(filePath);
    const fileSize = stat.size;
    const range = req.headers.range;

    if (range) {
        const parts = range.replace(/bytes=/, "").split("-");
        const start = parseInt(parts[0], 10);
        const end = parts[1] ? parseInt(parts[1], 10) : fileSize - 1;
        const chunksize = (end - start) + 1;
        const file = fs.createReadStream(filePath, { start, end });
        const head = {
            'Content-Range': `bytes ${start}-${end}/${fileSize}`,
            'Accept-Ranges': 'bytes',
            'Content-Length': chunksize,
            'Content-Type': 'video/mp2t',
        };
        res.writeHead(206, head);
        file.pipe(res);
    } else {
        const head = {
            'Content-Length': fileSize,
            'Content-Type': 'video/mp2t',
        };
        res.writeHead(200, head);
        fs.createReadStream(filePath).pipe(res);
    }
});

// WebSocket connection handling
wss.on('connection', (ws) => {
    console.log('WebSocket client connected');
    
    ws.on('close', () => {
        console.log('WebSocket client disconnected');
    });
    
    ws.on('error', (error) => {
        console.error('WebSocket error:', error);
    });
});

// Start server
app.listen(PORT, () => {
    console.log(`🚀 RTSP Compression Dashboard server running on port ${PORT}`);
    console.log(`📊 WebSocket server running on port 8080`);
    console.log(`🌐 Open http://localhost:${PORT} to access the dashboard`);
});
